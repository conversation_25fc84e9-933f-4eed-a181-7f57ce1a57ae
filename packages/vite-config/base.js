import path from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import { defineConfig, splitVendorChunkPlugin } from 'vite'
import { checker } from 'vite-plugin-checker'

/**
 * Base Vite configuration with common settings
 * @param {Object} options - Configuration options
 * @param {string} options.root - Root directory
 * @param {string} [options.srcDir='src'] - Source directory
 * @param {string} [options.outDir='dist'] - Output directory
 * @param {Object} [options.alias={}] - Additional aliases
 * @param {Array} [options.plugins=[]] - Additional plugins
 * @param {Object} [options.define={}] - Global constants
 * @param {boolean} [options.sourcemap=false] - Generate sourcemaps
 * @param {boolean} [options.analyze=false] - Bundle analyzer
 * @param {boolean} [options.splitChunks=true] - Enable smart chunk splitting
 * @param {Object} [options.performanceBudget={}] - Performance budget limits
 * @returns {Object} Vite configuration
 */
export function createBaseConfig(options = {}) {
  const {
    root = process.cwd(),
    srcDir = 'src',
    outDir = 'dist',
    alias = {},
    plugins = [],
    define = {},
    sourcemap = false,
    analyze = false,
    splitChunks = true,
    performanceBudget = {
      maxAssetSize: 500000, // 500KB
      maxEntrypointSize: 800000, // 800KB
    },
  } = options

  const basePlugins = [
    checker({
      typescript: true,
      // ESLint disabled due to compatibility issues with newer ESLint versions
      // Applications should handle ESLint separately
    }),
  ]

  // Enable smart chunk splitting
  if (splitChunks) {
    basePlugins.push(splitVendorChunkPlugin())
  }

  if (analyze) {
    basePlugins.push(
      visualizer({
        filename: 'dist/stats.html',
        open: true,
        gzipSize: true,
        brotliSize: true,
        template: 'treemap',
      })
    )
  }

  return defineConfig({
    root,

    resolve: {
      alias: {
        '@': path.resolve(root, srcDir),
        '@luminar/shared-ui': path.resolve(root, '../../packages/shared-ui/src'),
        '@luminar/shared-auth': path.resolve(root, '../../packages/shared-auth/src'),
        '@luminar/shared-core': path.resolve(root, '../../packages/shared-core/src'),
        '@luminar/shared-config': path.resolve(root, '../../packages/shared-config/src'),
        '@luminar/testing': path.resolve(root, '../../packages/testing/src'),
        '@luminar/debug-utils': path.resolve(root, '../../packages/debug-utils'),
        '@luminar/runtime-config': path.resolve(root, '../../packages/runtime-config/src'),
        '@luminar/agent-orchestration': path.resolve(root, '../../packages/agent-orchestration/src'),
        '@luminar/vite-config': path.resolve(root, '../../packages/vite-config'),
        ...alias,
      },
    },

    define: {
      __APP_VERSION__: JSON.stringify(process.env?.npm_package_version || '1.0.0'),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
      // Define process.env for client-side code
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
      global: 'globalThis',
      ...define,
    },

    plugins: [...basePlugins, ...plugins],

    build: {
      outDir,
      sourcemap,
      target: 'esnext',
      minify: 'esbuild',
      cssTarget: 'chrome80',

      // Performance budget configuration
      chunkSizeWarningLimit: performanceBudget.maxEntrypointSize / 1000, // in KB

      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // Dynamic chunk splitting based on module path
            if (id.includes('node_modules')) {
              // Core React dependencies
              if (id.includes('react') || id.includes('react-dom')) {
                return 'vendor-react'
              }
              // Router and query dependencies
              if (id.includes('@tanstack/react-router') || id.includes('@tanstack/react-query')) {
                return 'vendor-router'
              }
              // UI components
              if (
                id.includes('@radix-ui') ||
                id.includes('lucide-react') ||
                id.includes('framer-motion')
              ) {
                return 'vendor-ui'
              }
              // Chart libraries
              if (id.includes('recharts') || id.includes('d3')) {
                return 'vendor-charts'
              }
              // Utilities
              if (
                id.includes('clsx') ||
                id.includes('class-variance-authority') ||
                id.includes('tailwind-merge') ||
                id.includes('date-fns')
              ) {
                return 'vendor-utils'
              }
              // Other vendor libraries
              return 'vendor-misc'
            }
            // Code splitting for large feature modules
            if (id.includes('/routes/stats/') || id.includes('/components/charts/')) {
              return 'analytics'
            }
            if (id.includes('/routes/automation/') || id.includes('/components/rules/')) {
              return 'automation'
            }
            if (id.includes('/routes/assistant/') || id.includes('/components/assistant-chat/')) {
              return 'assistant'
            }
            if (
              id.includes('/routes/bulk-unsubscribe/') ||
              id.includes('/components/bulk-actions/')
            ) {
              return 'bulk-actions'
            }
          },
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId
              ? chunkInfo.facadeModuleId
                  .split('/')
                  .pop()
                  .replace(/\.[^.]*$/, '')
              : 'chunk'
            return `js/${facadeModuleId}-[hash].js`
          },
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.')
            const ext = info[info.length - 1]
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
              return `images/[name]-[hash][extname]`
            }
            if (/css/i.test(ext)) {
              return `css/[name]-[hash][extname]`
            }
            return `assets/[name]-[hash][extname]`
          },
        },
      },

      // Optimize dependencies
      commonjsOptions: {
        include: [/node_modules/],
      },

      // Advanced minification
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      },
    },

    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        '@tanstack/react-router',
        '@tanstack/react-query',
        'zustand',
        'axios',
        'clsx',
        'class-variance-authority',
        'tailwind-merge',
        'lucide-react',
        '@radix-ui/react-slot',
        'framer-motion',
        'recharts',
        'date-fns',
        'lodash',
        'immer',
        'jotai',
        '@luminar/shared-ui',
        '@luminar/shared-config',
      ],
      exclude: ['@luminar/shared-ui', '@luminar/shared-auth', '@luminar/shared-core', '@luminar/shared-config'],
      entries: ['src/main.tsx', 'src/**/*.tsx', 'src/**/*.ts'],
      esbuildOptions: {
        // Node.js global to browser globalThis
        define: {
          global: 'globalThis',
        },
      },
    },

    server: {
      port: 3000,
      strictPort: false,
      host: true,
      open: false,
      cors: true,
    },

    preview: {
      port: 4173,
      strictPort: false,
      host: true,
      open: false,
    },

    // CSS configuration
    css: {
      modules: {
        localsConvention: 'camelCaseOnly',
      },
      postcss: {
        plugins: [],
      },
    },

    // Environment configuration
    envPrefix: ['VITE_', 'LUMINAR_'],

    // Worker configuration
    worker: {
      format: 'es',
    },

    // Experimental features
    experimental: {
      renderBuiltUrl(filename) {
        return `/${filename}`
      },
    },
  })
}
