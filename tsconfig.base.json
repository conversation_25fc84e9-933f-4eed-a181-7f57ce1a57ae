{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitReturns": true, "noImplicitOverride": true, "strictPropertyInitialization": true, "noUncheckedIndexedAccess": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "noImplicitThis": true, "alwaysStrict": true, "noPropertyAccessFromIndexSignature": true, "paths": {"@luminar/shared-ui": ["./packages/shared-ui/src"], "@luminar/shared-ui/*": ["./packages/shared-ui/src/*"], "@luminar/shared-auth": ["./packages/shared-auth/src"], "@luminar/shared-auth/*": ["./packages/shared-auth/src/*"], "@luminar/shared-core": ["./packages/shared-core/src"], "@luminar/shared-core/*": ["./packages/shared-core/src/*"], "@luminar/shared-config": ["./packages/shared-config/src"], "@luminar/shared-config/*": ["./packages/shared-config/src/*"], "@luminar/testing": ["./packages/testing/src"], "@luminar/testing/*": ["./packages/testing/src/*"], "@luminar/debug-utils": ["./packages/debug-utils"], "@luminar/debug-utils/*": ["./packages/debug-utils/*"], "@luminar/runtime-config": ["./packages/runtime-config/src"], "@luminar/runtime-config/*": ["./packages/runtime-config/src/*"], "@luminar/agent-orchestration": ["./packages/agent-orchestration/src"], "@luminar/agent-orchestration/*": ["./packages/agent-orchestration/src/*"], "@luminar/vite-config": ["./packages/vite-config"], "@luminar/vite-config/*": ["./packages/vite-config/*"], "@packages/*": ["./packages/*/src"], "@apps/command-center/*": ["./apps/command-center/src/*"], "@apps/shell/*": ["./apps/shell/src/*"], "@apps/amna/*": ["./apps/amna/src/*"], "@apps/e-connect/*": ["./apps/e-connect/src/*"], "@apps/lighthouse/*": ["./apps/lighthouse/src/*"], "@apps/training-need-analysis/*": ["./apps/training-need-analysis/src/*"], "@apps/vendors/*": ["./apps/vendors/src/*"], "@apps/wins-of-week/*": ["./apps/wins-of-week/src/*"], "@api/*": ["./apps/command-center/src/*"], "@api/types": ["./apps/command-center/src/types"], "@api/schemas": ["./apps/command-center/src/schemas"], "@api/dtos": ["./apps/command-center/src/dtos"]}}, "include": ["apps/**/*", "packages/**/*", "infrastructure/**/*", "tools/**/*"], "exclude": ["node_modules", "dist", "build", ".next", ".nuxt", ".output", "coverage"]}