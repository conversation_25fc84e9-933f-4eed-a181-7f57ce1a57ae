import { HttpResponse, http } from 'msw'
import type {
  ApiResponse,
  ChatConversation,
  PaginatedResponse,
  SendChatRequest,
  SendChatResponse,
  User,
  UserPreferences,
  WebSearchResult,
} from '@/api/types'
import { ENDPOINTS } from '@/config/api.config'

// Mock data
const mockUser: User = {
  id: 'user_123',
  email: '<EMAIL>',
  name: 'Test User',
  avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=test',
  preferences: {
    theme: 'system',
    language: 'en',
    notifications: {
      email: true,
      push: false,
    },
    chatSettings: {
      streamResponses: true,
      showSources: true,
      enableFollowUps: true,
    },
  },
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
}

const mockConversations: ChatConversation[] = [
  {
    id: 'conv_123',
    userId: 'user_123',
    title: 'Test Conversation',
    messages: [
      {
        id: 'msg_1',
        conversationId: 'conv_123',
        role: 'user',
        content: 'Hello, how are you?',
        createdAt: '2024-01-01T00:00:00Z',
      },
      {
        id: 'msg_2',
        conversationId: 'conv_123',
        role: 'assistant',
        content: "I'm doing well, thank you! How can I help you today?",
        metadata: {
          responseType: 'general',
          processingTime: 500,
        },
        createdAt: '2024-01-01T00:00:01Z',
      },
    ],
    metadata: {
      archived: false,
      starred: false,
      lastMessageAt: '2024-01-01T00:00:01Z',
      messageCount: 2,
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:01Z',
  },
]

// Helper to create API response
function createApiResponse<T>(data: T): ApiResponse<T> {
  return {
    data,
    status: 'success',
    timestamp: new Date().toISOString(),
  }
}

// Helper to create paginated response
function createPaginatedResponse<T>(
  data: T[],
  page: number = 1,
  limit: number = 10
): PaginatedResponse<T> {
  return {
    data,
    pagination: {
      page,
      limit,
      total: data.length,
      totalPages: Math.ceil(data.length / limit),
    },
  }
}

// API handlers
export const handlers = [
  // Chat endpoints
  http.post(`${ENDPOINTS.chat.send}`, async ({ request }) => {
    const body = (await request.json()) as SendChatRequest

    const response: SendChatResponse = {
      message: {
        id: `msg_${Date.now()}`,
        conversationId: body.conversationId || `conv_${Date.now()}`,
        role: 'assistant',
        content: `This is a mock response to: ${body.message}`,
        metadata: {
          responseType: 'general',
          processingTime: 100,
          model: 'test-model',
        },
        createdAt: new Date().toISOString(),
      },
      conversationId: body.conversationId || `conv_${Date.now()}`,
      usage: {
        promptTokens: 10,
        completionTokens: 20,
        totalTokens: 30,
      },
    }

    return HttpResponse.json(createApiResponse(response))
  }),

  http.get(`${ENDPOINTS.chat.history}`, ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')

    return HttpResponse.json(createPaginatedResponse(mockConversations, page, limit))
  }),

  http.get(`${ENDPOINTS.chat.history}/:id`, ({ params }) => {
    const conversation = mockConversations.find((c) => c.id === params.id)

    if (!conversation) {
      return new HttpResponse(null, { status: 404 })
    }

    return HttpResponse.json(createApiResponse(conversation))
  }),

  http.delete(`${ENDPOINTS.chat.delete(':id')}`, () => {
    return new HttpResponse(null, { status: 204 })
  }),

  http.post(`${ENDPOINTS.chat.feedback(':id')}`, () => {
    return new HttpResponse(null, { status: 204 })
  }),

  // User endpoints
  http.get(`${ENDPOINTS.user.profile}`, () => {
    return HttpResponse.json(createApiResponse(mockUser))
  }),

  http.patch(`${ENDPOINTS.user.profile}`, async ({ request }) => {
    const updates = await request.json()
    const updatedUser = { ...mockUser, ...updates }
    return HttpResponse.json(createApiResponse(updatedUser))
  }),

  http.get(`${ENDPOINTS.user.preferences}`, () => {
    return HttpResponse.json(createApiResponse(mockUser.preferences))
  }),

  http.patch(`${ENDPOINTS.user.preferences}`, async ({ request }) => {
    const updates = (await request.json()) as Partial<UserPreferences>
    const updatedPreferences = { ...mockUser.preferences, ...updates }
    return HttpResponse.json(createApiResponse(updatedPreferences))
  }),

  // Search endpoints
  http.post(`${ENDPOINTS.search.web}`, async ({ request }) => {
    const body = (await request.json()) as { query: string }

    const mockSearchResult: WebSearchResult = {
      results: [
        {
          title: `Result for ${body.query}`,
          url: 'https://example.com',
          snippet: 'This is a mock search result',
          source: 'example.com',
          relevanceScore: 0.95,
        },
      ],
      query: body.query,
      totalResults: 1,
      searchTime: 0.5,
    }

    return HttpResponse.json(createApiResponse(mockSearchResult))
  }),

  // Error scenarios
  http.get('/api/error/500', () => {
    return new HttpResponse(null, { status: 500 })
  }),

  http.get('/api/error/404', () => {
    return new HttpResponse(null, { status: 404 })
  }),

  http.get('/api/error/401', () => {
    return new HttpResponse(null, { status: 401 })
  }),

  http.get('/api/error/rate-limit', () => {
    return new HttpResponse(null, {
      status: 429,
      headers: {
        'Retry-After': '60',
      },
    })
  }),
]
