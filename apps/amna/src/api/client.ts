import { createLuminarAP<PERSON>lient, type LuminarAP<PERSON>lient } from '@luminar/shared-core'
import { API_CONFIG, HEADERS } from '@/config/api.config'
import { ApiError } from './errors'

// Create and configure the shared API client instance
export const apiClient: LuminarAPIClient = createLuminarAPIClient({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  defaultHeaders: HEADERS,
  retries: API_CONFIG.retries,
  retryDelay: API_CONFIG.retryDelay,
  enableLogging: process.env.NODE_ENV === 'development',
})

// Add auth token interceptor
apiClient.addRequestInterceptor({
  onRequest: async (config) => {
    const token = localStorage.getItem('auth_token')
    if (token && !config.headers['skipAuth']) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
})

// Add response error interceptor for auth handling
apiClient.addResponseInterceptor({
  onResponseError: async (error) => {
    // Handle auth errors
    if (error instanceof Error && 'response' in error) {
      const axiosError = error as any
      if (axiosError.response?.status === 401) {
        // Clear auth token and redirect to login
        localStorage.removeItem('auth_token')
        window.location.href = '/login'
      }
    }
    
    // Convert to our ApiError format if needed
    if (!(error instanceof ApiError)) {
      return ApiError.fromError(error)
    }
    
    return error
  },
})

// Helper functions for auth token management
export function setAuthToken(token: string | null) {
  if (token) {
    localStorage.setItem('auth_token', token)
  } else {
    localStorage.removeItem('auth_token')
  }
}

export function getAuthToken(): string | null {
  return localStorage.getItem('auth_token')
}

// Re-export types and the client for backward compatibility
export type { LuminarAPIClient as ApiClient }
export default apiClient