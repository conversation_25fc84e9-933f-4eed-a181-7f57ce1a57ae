import { faker } from '@faker-js/faker'
import type { ChatConversation, ChatMessage, User } from '@/api/types'
import type {
  ConversationAnalytics,
  TimeSeriesData,
  TopicAnalytics,
} from '../../services/analyticsService'
import type { Card, Dashboard, Widget } from '../../services/componentService'
import type { EmailStatus, EmailTemplate, Notification } from '../../services/notificationService'

// User Factory
export const userFactory = {
  create(overrides?: Partial<User>): User {
    const firstName = faker.person.firstName()
    const lastName = faker.person.lastName()
    return {
      id: faker.string.uuid(),
      email: faker.internet.email({ firstName, lastName }),
      name: `${firstName} ${lastName}`,
      avatar: faker.image.avatar(),
      preferences: {
        theme: faker.helpers.arrayElement(['light', 'dark', 'system']),
        language: faker.helpers.arrayElement(['en', 'es', 'fr', 'de']),
        notifications: {
          email: faker.datatype.boolean(),
          push: faker.datatype.boolean(),
        },
        chatSettings: {
          streamResponses: faker.datatype.boolean({ probability: 0.8 }),
          showSources: faker.datatype.boolean({ probability: 0.9 }),
          enableFollowUps: faker.datatype.boolean({ probability: 0.7 }),
        },
      },
      createdAt: faker.date.past().toISOString(),
      updatedAt: faker.date.recent().toISOString(),
      ...overrides,
    }
  },

  createMany(count: number): User[] {
    return Array.from({ length: count }, () => this.create())
  },
}

// Chat Factory
export const chatFactory = {
  createMessage(overrides?: Partial<ChatMessage>): ChatMessage {
    return {
      id: faker.string.uuid(),
      conversationId: faker.string.uuid(),
      role: faker.helpers.arrayElement(['user', 'assistant']),
      content: faker.lorem.paragraph(),
      metadata: {
        responseType: faker.helpers.arrayElement([
          'web_search',
          'component_analysis',
          'performance',
          'comparison',
          'general',
        ]),
        processingTime: faker.number.int({ min: 100, max: 2000 }),
        model: faker.helpers.arrayElement(['gpt-4', 'claude-3', 'gemini-pro']),
      },
      createdAt: faker.date.recent().toISOString(),
      ...overrides,
    }
  },

  createConversation(overrides?: Partial<ChatConversation>): ChatConversation {
    const messageCount = faker.number.int({ min: 2, max: 20 })
    const conversationId = faker.string.uuid()
    const messages = Array.from({ length: messageCount }, (_, i) =>
      this.createMessage({
        conversationId,
        role: i % 2 === 0 ? 'user' : 'assistant',
      })
    )

    return {
      id: conversationId,
      userId: faker.string.uuid(),
      title: faker.lorem.sentence({ min: 3, max: 8 }),
      messages,
      metadata: {
        archived: faker.datatype.boolean({ probability: 0.1 }),
        starred: faker.datatype.boolean({ probability: 0.2 }),
        lastMessageAt: messages[messages.length - 1].createdAt,
        messageCount,
      },
      createdAt: faker.date.past().toISOString(),
      updatedAt: faker.date.recent().toISOString(),
      ...overrides,
    }
  },
}

// Notification Factory
export const notificationFactory = {
  create(overrides?: Partial<Notification>): Notification {
    const categories = ['system', 'chat', 'account', 'social', 'marketing'] as const
    const types = ['info', 'success', 'warning', 'error'] as const

    return {
      id: faker.string.uuid(),
      userId: faker.string.uuid(),
      title: faker.lorem.sentence({ min: 3, max: 8 }),
      message: faker.lorem.paragraph({ min: 1, max: 3 }),
      type: faker.helpers.arrayElement(types),
      category: faker.helpers.arrayElement(categories),
      read: faker.datatype.boolean({ probability: 0.4 }),
      actionUrl: faker.datatype.boolean({ probability: 0.6 }) ? faker.internet.url() : undefined,
      actionText: faker.datatype.boolean({ probability: 0.6 }) ? faker.lorem.words(2) : undefined,
      createdAt: faker.date.recent().toISOString(),
      ...overrides,
    }
  },

  createEmailTemplate(overrides?: Partial<EmailTemplate>): EmailTemplate {
    const categories = ['transactional', 'marketing', 'system', 'welcome'] as const
    const templateNames = [
      'welcome',
      'password-reset',
      'email-verification',
      'weekly-summary',
      'security-alert',
      'feature-announcement',
    ]

    return {
      id: faker.helpers.arrayElement(templateNames),
      name: faker.lorem.sentence({ min: 2, max: 5 }),
      subject: faker.lorem.sentence({ min: 3, max: 10 }),
      body: faker.lorem.paragraphs(3, '<br/><br/>'),
      variables: faker.helpers.arrayElements(
        ['userName', 'appName', 'resetLink', 'verificationLink', 'loginTime'],
        { min: 2, max: 4 }
      ),
      category: faker.helpers.arrayElement(categories),
      isActive: true,
      createdAt: faker.date.past().toISOString(),
      updatedAt: faker.date.recent().toISOString(),
      ...overrides,
    }
  },

  createEmailStatus(overrides?: Partial<EmailStatus>): EmailStatus {
    const statuses = ['pending', 'sent', 'delivered', 'bounced', 'failed'] as const
    const status = faker.helpers.arrayElement(statuses)

    return {
      id: faker.string.uuid(),
      to: faker.helpers.arrayElements(
        Array.from({ length: 5 }, () => faker.internet.email()),
        { min: 1, max: 3 }
      ),
      subject: faker.lorem.sentence({ min: 3, max: 10 }),
      status,
      sentAt: faker.date.recent().toISOString(),
      deliveredAt: status === 'delivered' ? faker.date.recent().toISOString() : undefined,
      openedAt:
        status === 'delivered' && faker.datatype.boolean({ probability: 0.6 })
          ? faker.date.recent().toISOString()
          : undefined,
      error: ['bounced', 'failed'].includes(status) ? faker.lorem.sentence() : undefined,
      ...overrides,
    }
  },
}

// Component Factory
export const componentFactory = {
  createCard(overrides?: Partial<Card>): Card {
    const types = ['info', 'chat', 'media', 'action', 'stats'] as const
    const type = faker.helpers.arrayElement(types)

    return {
      id: faker.string.uuid(),
      type,
      title: faker.lorem.sentence({ min: 3, max: 8 }),
      subtitle: faker.datatype.boolean({ probability: 0.6 })
        ? faker.lorem.sentence({ min: 5, max: 12 })
        : undefined,
      content:
        type === 'stats'
          ? {
              metric1: faker.number.int({ min: 0, max: 1000 }),
              metric2: faker.number.float({ min: 0, max: 100, fractionDigits: 1 }),
              metric3: faker.number.int({ min: 0, max: 50 }),
            }
          : faker.lorem.paragraph(),
      media:
        type === 'media'
          ? {
              type: faker.helpers.arrayElement(['image', 'video', 'audio']),
              url: faker.image.url(),
              thumbnail: faker.image.url(),
            }
          : undefined,
      expanded: faker.datatype.boolean({ probability: 0.3 }),
      pinned: faker.datatype.boolean({ probability: 0.2 }),
      order: faker.number.int({ min: 0, max: 20 }),
      createdAt: faker.date.past().toISOString(),
      updatedAt: faker.date.recent().toISOString(),
      ...overrides,
    }
  },

  createWidget(overrides?: Partial<Widget>): Widget {
    const types = ['chart', 'table', 'list', 'calendar', 'map', 'custom'] as const

    return {
      id: faker.string.uuid(),
      type: faker.helpers.arrayElement(types),
      name: faker.lorem.sentence({ min: 2, max: 5 }),
      description: faker.lorem.sentence({ min: 5, max: 15 }),
      config: {
        dataSource: faker.internet.url(),
        displayOptions: {
          theme: faker.helpers.arrayElement(['light', 'dark', 'auto']),
          showLegend: faker.datatype.boolean(),
          sortable: faker.datatype.boolean(),
        },
      },
      refreshInterval: faker.helpers.arrayElement([30000, 60000, 300000, 600000]),
      interactive: faker.datatype.boolean({ probability: 0.8 }),
      resizable: faker.datatype.boolean({ probability: 0.7 }),
      position: {
        x: faker.number.int({ min: 0, max: 11 }),
        y: faker.number.int({ min: 0, max: 20 }),
        width: faker.number.int({ min: 2, max: 6 }),
        height: faker.number.int({ min: 2, max: 6 }),
      },
      createdAt: faker.date.past().toISOString(),
      updatedAt: faker.date.recent().toISOString(),
      ...overrides,
    }
  },

  createDashboard(overrides?: Partial<Dashboard>): Dashboard {
    const widgetCount = faker.number.int({ min: 3, max: 8 })

    return {
      id: faker.string.uuid(),
      name: faker.lorem.sentence({ min: 2, max: 5 }),
      description: faker.lorem.sentence({ min: 5, max: 15 }),
      layout: {
        type: faker.helpers.arrayElement(['grid', 'flex', 'masonry']),
        columns: faker.helpers.arrayElement([8, 12, 16]),
        gap: faker.helpers.arrayElement([12, 16, 20, 24]),
      },
      widgets: Array.from({ length: widgetCount }, () => faker.string.uuid()),
      theme: faker.helpers.arrayElement(['light', 'dark', 'auto']),
      public: faker.datatype.boolean({ probability: 0.3 }),
      createdAt: faker.date.past().toISOString(),
      updatedAt: faker.date.recent().toISOString(),
      ...overrides,
    }
  },
}

// Analytics Factory
export const analyticsFactory = {
  createConversationAnalytics(overrides?: Partial<ConversationAnalytics>): ConversationAnalytics {
    return {
      conversationId: faker.string.uuid(),
      title: faker.lorem.sentence({ min: 3, max: 8 }),
      messageCount: faker.number.int({ min: 5, max: 100 }),
      duration: faker.number.int({ min: 300, max: 7200 }),
      topics: faker.helpers.arrayElements(
        ['programming', 'ai', 'data-science', 'web-dev', 'debugging', 'architecture'],
        { min: 1, max: 4 }
      ),
      sentiment: faker.helpers.arrayElement(['positive', 'neutral', 'negative']),
      satisfaction: faker.number.float({ min: 1, max: 5, fractionDigits: 1 }),
      createdAt: faker.date.past().toISOString(),
      ...overrides,
    }
  },

  createTopicAnalytics(overrides?: Partial<TopicAnalytics>): TopicAnalytics {
    const topics = [
      'programming',
      'artificial-intelligence',
      'data-science',
      'web-development',
      'mathematics',
      'writing',
      'business',
      'science',
      'language-learning',
      'health',
    ]

    return {
      topic: faker.helpers.arrayElement(topics),
      count: faker.number.int({ min: 100, max: 5000 }),
      percentage: faker.number.float({ min: 1, max: 30, fractionDigits: 1 }),
      trend: faker.helpers.arrayElement(['up', 'down', 'stable']),
      relatedTopics: faker.helpers.arrayElements(topics, { min: 2, max: 5 }),
      ...overrides,
    }
  },

  createTimeSeriesData(
    points: number,
    options?: {
      minValue?: number
      maxValue?: number
      label?: string
      trend?: 'up' | 'down' | 'stable' | 'random'
    }
  ): TimeSeriesData[] {
    const { minValue = 0, maxValue = 100, label = 'Value', trend = 'random' } = options || {}

    let currentValue = (minValue + maxValue) / 2
    const stepSize = (maxValue - minValue) / 20

    return Array.from({ length: points }, (_, i) => {
      // Apply trend
      switch (trend) {
        case 'up':
          currentValue += stepSize * (0.5 + Math.random() * 0.5)
          break
        case 'down':
          currentValue -= stepSize * (0.5 + Math.random() * 0.5)
          break
        case 'stable':
          currentValue += stepSize * (Math.random() - 0.5) * 0.2
          break
        default:
          currentValue += stepSize * (Math.random() - 0.5) * 2
      }

      // Keep within bounds
      currentValue = Math.max(minValue, Math.min(maxValue, currentValue))

      return {
        timestamp: faker.date.recent({ days: points - i }).toISOString(),
        value: parseFloat(currentValue.toFixed(2)),
        label,
      }
    }).reverse()
  },
}

// Seed data generators
export const seedData = {
  users: (count: number = 10) => userFactory.createMany(count),

  conversations: (count: number = 20) =>
    Array.from({ length: count }, () => chatFactory.createConversation()),

  notifications: (count: number = 50) =>
    Array.from({ length: count }, () => notificationFactory.create()),

  cards: (count: number = 15) => Array.from({ length: count }, () => componentFactory.createCard()),

  widgets: (count: number = 10) =>
    Array.from({ length: count }, () => componentFactory.createWidget()),

  dashboards: (count: number = 5) =>
    Array.from({ length: count }, () => componentFactory.createDashboard()),

  analytics: {
    conversations: (count: number = 30) =>
      Array.from({ length: count }, () => analyticsFactory.createConversationAnalytics()),

    topics: (count: number = 10) =>
      Array.from({ length: count }, () => analyticsFactory.createTopicAnalytics()),

    timeSeries: (points: number = 30, options?: Parameters<typeof analyticsFactory.createTimeSeriesData>[1]) =>
      analyticsFactory.createTimeSeriesData(points, options),
  },
}

// Batch operations
export const batchCreate = {
  async users(count: number) {
    return Promise.all(Array.from({ length: count }, () => Promise.resolve(userFactory.create())))
  },

  async notifications(userId: string, count: number) {
    return Promise.all(
      Array.from({ length: count }, () => Promise.resolve(notificationFactory.create({ userId })))
    )
  },

  async conversationHistory(userId: string, count: number) {
    return Promise.all(
      Array.from({ length: count }, () =>
        Promise.resolve(chatFactory.createConversation({ userId }))
      )
    )
  },
}
