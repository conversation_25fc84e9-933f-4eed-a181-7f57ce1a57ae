// Export all mock handlers

// Export browser setup
export { worker, workerOptions } from './browser'
export * from './data/components'
export * from './data/notifications'

// Export mock data
export * from './data/users'
// Export factories
export * from './factories'
export { analyticsHandlers } from './handlers/analytics'
export { authHandlers } from './handlers/auth'
export { componentHandlers } from './handlers/components'
export { notificationHandlers } from './handlers/notifications'

import { handlers as testHandlers } from '@/test/mocks/handlers'
import { analyticsHandlers } from './handlers/analytics'
// Combined handlers for easy import
import { authHandlers } from './handlers/auth'
import { componentHandlers } from './handlers/components'
import { notificationHandlers } from './handlers/notifications'

export const allHandlers = [
  ...authHandlers,
  ...notificationHandlers,
  ...componentHandlers,
  ...analyticsHandlers,
  ...testHandlers,
]

// Mock API feature flags
export const mockApiConfig = {
  enabled: import.meta.env.VITE_USE_MOCK_API === 'true',
  delayMin: 100,
  delayMax: 1000,
  errorRate: 0.02, // 2% error rate for testing
  logging: import.meta.env.DEV,
}

// Utility to add realistic delays
export const mockDelay = async (min = mockApiConfig.delayMin, max = mockApiConfig.delayMax) => {
  const delay = Math.random() * (max - min) + min
  await new Promise((resolve) => setTimeout(resolve, delay))
}

// Utility to simulate random errors
export const mockError = (rate = mockApiConfig.errorRate): boolean => {
  return Math.random() < rate
}

// Mock authentication state
export const mockAuthState = {
  isAuthenticated: false,
  token: null as string | null,
  user: null as { id: string; name: string; email: string } | null,

  login(token: string, user: { id: string; name: string; email: string }) {
    this.isAuthenticated = true
    this.token = token
    this.user = user
    localStorage.setItem('mock_token', token)
    localStorage.setItem('mock_user', JSON.stringify(user))
  },

  logout() {
    this.isAuthenticated = false
    this.token = null
    this.user = null
    localStorage.removeItem('mock_token')
    localStorage.removeItem('mock_user')
  },

  restore() {
    const token = localStorage.getItem('mock_token')
    const userStr = localStorage.getItem('mock_user')
    if (token && userStr) {
      this.isAuthenticated = true
      this.token = token
      this.user = JSON.parse(userStr)
    }
  },
}

// Initialize auth state
if (mockApiConfig.enabled) {
  mockAuthState.restore()
}
