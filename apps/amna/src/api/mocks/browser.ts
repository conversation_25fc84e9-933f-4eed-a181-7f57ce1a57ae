import { setupWorker } from 'msw/browser'
import { handlers as testHandlers } from '@/test/mocks/handlers'
import { analyticsHandlers } from './handlers/analytics'
import { authHandlers } from './handlers/auth'
import { componentHandlers } from './handlers/components'
import { notificationHandlers } from './handlers/notifications'
import { reminderHandlers } from './handlers/reminders'

// Combine all handlers
const allHandlers = [
  ...authHandlers,
  ...notificationHandlers,
  ...componentHandlers,
  ...analyticsHandlers,
  ...reminderHandlers,
  ...testHandlers, // Include existing test handlers
]

// Create the worker
export const worker = setupWorker(...allHandlers)

// Configuration options
export const workerOptions = {
  onUnhandledRequest: 'bypass' as const,
  serviceWorker: {
    url: '/mockServiceWorker.js',
    options: {
      scope: '/',
    },
  },
}
