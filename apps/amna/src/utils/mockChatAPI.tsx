import type { Message, Source } from '@/components/chat/ChatMessages'

interface MessageMetadata {
  language?: string
  fileName?: string
  fileSize?: string
  fileType?: string
  imageUrl?: string
  imageAlt?: string
  personality?: {
    id: string
    name: string
    role: string
    style: string
  }
}

interface ChatResponse {
  content: string
  sources?: Source[]
  followUpQuestions?: string[]
  type?: 'technical' | 'creative' | 'casual' | 'research' | 'troubleshooting' | 'web_search' | 'component_analysis' | 'performance' | 'comparison' | 'general'
  processingTime?: number
  messageType?: 'text' | 'code' | 'file' | 'image' | 'table' | 'list' | 'structured'
  personality?: AIPersonality
  metadata?: MessageMetadata
}

interface AIPersonality {
  id: string
  name: string
  role: string
  style: 'professional' | 'friendly' | 'analytical' | 'creative' | 'mentor'
  traits: string[]
  responsePattern: {
    greeting: string[]
    thinking: string[]
    explanation: string[]
    encouragement: string[]
  }
}

interface ConversationScenario {
  keywords: string[]
  type: 'technical' | 'creative' | 'casual' | 'research' | 'troubleshooting'
  generator: (message: string) => ChatResponse
}

class MockChatAPI {
  private chatHistory: Record<string, Message[]> = {}
  private conversationScenarios: ConversationScenario[] = []
  private personalities: AIPersonality[] = []
  private currentPersonality: AIPersonality | null = null

  constructor() {
    this.initializePersonalities()
    this.initializeConversationScenarios()
    this.selectRandomPersonality()
  }

  sendMessage(message: string): ChatResponse {
    const startTime = Date.now()
    const lowerMessage = message.toLowerCase()

    // Map mock types to API types
    const typeMap: Record<string, string> = {
      'technical': 'web_search',
      'creative': 'component_analysis',
      'casual': 'general',
      'research': 'web_search',
      'troubleshooting': 'performance'
    }

    // Check for personality switching requests
    if (
      lowerMessage.includes('switch') &&
      (lowerMessage.includes('personality') || lowerMessage.includes('ai'))
    ) {
      return this.handlePersonalitySwitching(message)
    }

    // Randomly switch personality occasionally for variety (10% chance)
    if (Math.random() < 0.1) {
      this.selectRandomPersonality()
    }

    // Find matching scenario based on keywords
    const matchingScenario = this.conversationScenarios.find((scenario) =>
      scenario.keywords.some((keyword) => lowerMessage.includes(keyword))
    )

    let response: ChatResponse

    if (matchingScenario) {
      response = matchingScenario.generator(message)
      // Map the scenario type to the API type
      if (matchingScenario.type && typeMap[matchingScenario.type]) {
        response.type = typeMap[matchingScenario.type] as any
      } else {
        response.type = 'general'
      }
    } else {
      // Fallback to legacy logic with enhanced responses
      if (
        lowerMessage.includes('search') ||
        lowerMessage.includes('latest') ||
        lowerMessage.includes('trends')
      ) {
        response = this.generateWebSearchResponse(message)
        response.type = 'web_search'
      } else if (
        lowerMessage.includes('component') ||
        lowerMessage.includes('extract') ||
        lowerMessage.includes('project')
      ) {
        response = this.generateComponentAnalysisResponse(message)
        response.type = 'component_analysis'
      } else if (lowerMessage.includes('performance') || lowerMessage.includes('optimize')) {
        response = this.generatePerformanceResponse(message)
        response.type = 'performance'
      } else if (
        lowerMessage.includes('tanstack') ||
        lowerMessage.includes('next.js') ||
        lowerMessage.includes('react')
      ) {
        response = this.generateTechComparisonResponse(message)
        response.type = 'comparison'
      } else {
        response = this.generateGeneralResponse(message)
        response.type = 'general'
      }
    }

    // Add processing time simulation
    response.processingTime = Date.now() - startTime + Math.random() * 1000 + 500

    // Add personality info to response and metadata
    response.personality = this.currentPersonality || undefined
    if (this.currentPersonality) {
      response.metadata = {
        ...response.metadata,
        personality: {
          id: this.currentPersonality.id,
          name: this.currentPersonality.name,
          role: this.currentPersonality.role,
          style: this.currentPersonality.style,
        },
      }
    }

    // Apply personality styling to response
    if (this.currentPersonality) {
      response.content = this.applyPersonalityStyle(response.content, this.currentPersonality)
    }

    return response
  }

  private generateWebSearchResponse(_message: string): ChatResponse {
    const sources: Source[] = [
      {
        id: '1',
        title: 'React 19 Features and Updates - Official Documentation',
        url: 'https://react.dev/blog/2024/04/25/react-19',
        snippet:
          'React 19 introduces several new features including the React Compiler, Server Components improvements, and enhanced concurrent features for better performance.',
        favicon: 'https://react.dev/favicon.ico',
      },
      {
        id: '2',
        title: 'State of React 2024: Developer Survey Results',
        url: 'https://stateofjs.com/en-us/2024/libraries/front-end-frameworks',
        snippet:
          'The latest developer survey shows React maintaining its position as the most popular frontend framework, with growing adoption of Server Components and modern patterns.',
        favicon: 'https://stateofjs.com/favicon.ico',
      },
      {
        id: '3',
        title: 'Modern React Development Trends - Dev.to',
        url: 'https://dev.to/react-trends-2024',
        snippet:
          'Key trends include the rise of full-stack React frameworks, improved TypeScript integration, and the adoption of new state management solutions.',
        favicon: 'https://dev.to/favicon.ico',
      },
    ]

    return {
      content:
        "Based on the latest information, here are the current trends in React development:\n\n**React 19 Features**\nReact 19 has introduced significant improvements including the React Compiler for automatic optimization, enhanced Server Components, and better concurrent rendering capabilities.\n\n**Full-Stack Frameworks**\nThere's a growing trend toward full-stack React frameworks like Next.js, Remix, and TanStack Start, which provide better developer experience and performance out of the box.\n\n**TypeScript Integration**\nTypeScript adoption in React projects continues to grow, with improved type inference and better integration with modern React patterns.\n\n**State Management Evolution**\nDevelopers are moving toward simpler state management solutions, with Zustand and Jotai gaining popularity alongside traditional Redux Toolkit.",
      sources,
      followUpQuestions: [
        'What are the key differences between React 18 and 19?',
        'How do Server Components improve performance?',
        'Which React framework should I choose for my next project?',
      ],
    }
  }

  private generateComponentAnalysisResponse(_message: string): ChatResponse {
    return {
      content:
        "I've analyzed your current project structure and identified several opportunities for component extraction:\n\n**Reusable UI Components**\n- **FormField Component**: You can extract a reusable form field component that combines input, label, and error states\n- **DataTable Component**: Create a generic table component for displaying posts and users data\n- **LoadingSpinner Component**: Centralize loading states across your application\n\n**Layout Components**\n- **PageHeader Component**: Extract the common page header pattern used across routes\n- **NavigationMenu Component**: Create a reusable navigation component\n- **ContentWrapper Component**: Standardize content layout and spacing\n\n**Business Logic Components**\n- **ErrorBoundary Component**: You already have DefaultCatchBoundary, but consider creating specific error boundaries for different sections\n- **SEOHead Component**: Extract SEO metadata management into a reusable component\n\n**Recommended Implementation**\n1. Start with the most frequently used patterns\n2. Use TypeScript interfaces for consistent props\n3. Implement proper error handling and loading states\n4. Add Storybook for component documentation",
      followUpQuestions: [
        'Show me how to implement the FormField component',
        'What are the best practices for component composition?',
        'How should I structure my component folder hierarchy?',
      ],
    }
  }

  private generatePerformanceResponse(_message: string): ChatResponse {
    const sources: Source[] = [
      {
        id: '1',
        title: 'React Performance Optimization Guide',
        url: 'https://react.dev/learn/render-and-commit',
        snippet:
          "Learn about React's rendering process and how to optimize component performance using memoization, code splitting, and proper state management.",
        favicon: 'https://react.dev/favicon.ico',
      },
      {
        id: '2',
        title: 'Web Vitals and React Performance',
        url: 'https://web.dev/vitals/',
        snippet:
          'Core Web Vitals are essential metrics for measuring user experience. Learn how to optimize LCP, FID, and CLS in React applications.',
        favicon: 'https://web.dev/favicon.ico',
      },
    ]

    return {
      content:
        "Here are key strategies to improve your React application performance:\n\n**Code Splitting & Lazy Loading**\n- Use React.lazy() for route-based code splitting\n- Implement dynamic imports for heavy components\n- Consider using TanStack Router's built-in code splitting\n\n**Memoization Strategies**\n- Use React.memo for expensive component renders\n- Implement useMemo for expensive calculations\n- Apply useCallback for stable function references\n\n**Bundle Optimization**\n- Analyze bundle size with tools like Bundle Analyzer\n- Remove unused dependencies and code\n- Use tree shaking effectively\n\n**Server-Side Optimizations**\n- Implement proper caching strategies\n- Use TanStack Start's SSR capabilities\n- Optimize API calls and data fetching\n\n**Monitoring & Measurement**\n- Set up Core Web Vitals monitoring\n- Use React DevTools Profiler\n- Implement performance budgets",
      sources,
      followUpQuestions: [
        'How do I implement code splitting with TanStack Router?',
        'What are the best tools for measuring React performance?',
        'How can I optimize my bundle size?',
      ],
    }
  }

  private generateTechComparisonResponse(_message: string): ChatResponse {
    const sources: Source[] = [
      {
        id: '1',
        title: 'TanStack Start vs Next.js Comparison',
        url: 'https://tanstack.com/start/latest',
        snippet:
          'TanStack Start offers a type-safe, client-first approach to full-stack React development with powerful routing and state management.',
        favicon: 'https://tanstack.com/favicon.ico',
      },
      {
        id: '2',
        title: 'Modern React Framework Comparison 2024',
        url: 'https://blog.logrocket.com/react-frameworks-comparison/',
        snippet:
          'A comprehensive comparison of React frameworks including Next.js, Remix, TanStack Start, and others, focusing on performance and developer experience.',
        favicon: 'https://blog.logrocket.com/favicon.ico',
      },
    ]

    return {
      content:
        "Here's a detailed comparison between TanStack Start and Next.js:\n\n**TanStack Start Advantages**\n- **Type-Safe Routing**: Built-in TypeScript support with automatic route type generation\n- **Client-First Architecture**: Optimized for client-side interactions and state management\n- **Integrated Ecosystem**: Seamless integration with TanStack Query, Table, and other tools\n- **Flexible Deployment**: Not tied to specific hosting platforms\n\n**Next.js Advantages**\n- **Mature Ecosystem**: Larger community and extensive plugin ecosystem\n- **Vercel Integration**: Optimized deployment and edge functions\n- **App Router**: Modern routing with React Server Components\n- **Built-in Optimizations**: Image optimization, font optimization, and more\n\n**When to Choose TanStack Start**\n- Building type-safe applications with complex client-side state\n- Need tight integration with TanStack ecosystem\n- Prefer explicit over implicit configurations\n- Want more control over the build process\n\n**When to Choose Next.js**\n- Need mature ecosystem and community support\n- Building content-heavy or e-commerce sites\n- Want built-in optimizations out of the box\n- Prefer convention over configuration",
      sources,
      followUpQuestions: [
        'How does TanStack Start handle server-side rendering?',
        'What are the migration considerations from Next.js?',
        'Which framework is better for my specific use case?',
      ],
    }
  }

  private initializePersonalities(): void {
    this.personalities = [
      {
        id: 'alex-tech',
        name: 'Alex',
        role: 'Senior Technical Architect',
        style: 'professional',
        traits: ['precise', 'systematic', 'performance-focused', 'best-practices'],
        responsePattern: {
          greeting: [
            'Let me analyze this for you',
            "I'll provide a comprehensive solution",
            "Here's my technical assessment",
          ],
          thinking: [
            'Considering the architecture implications',
            'Evaluating performance trade-offs',
            'Analyzing the technical requirements',
          ],
          explanation: [
            'From a technical standpoint',
            'The implementation details are',
            "Here's how this works under the hood",
          ],
          encouragement: [
            'This is a solid approach',
            "You're on the right track",
            "That's a great technical question",
          ],
        },
      },
      {
        id: 'maya-creative',
        name: 'Maya',
        role: 'Creative Technology Lead',
        style: 'creative',
        traits: ['imaginative', 'user-focused', 'innovative', 'storytelling'],
        responsePattern: {
          greeting: [
            'What an interesting challenge!',
            "Let's explore this creatively",
            "I love where you're going with this",
          ],
          thinking: [
            'Imagining the user experience',
            'Considering the creative possibilities',
            'Thinking outside the conventional approach',
          ],
          explanation: [
            'Picture this scenario',
            'Think of it like',
            "Here's a creative way to approach it",
          ],
          encouragement: [
            "That's brilliantly creative!",
            "I'm excited by your vision",
            "You're thinking like a true innovator",
          ],
        },
      },
      {
        id: 'sam-mentor',
        name: 'Sam',
        role: 'Engineering Mentor',
        style: 'mentor',
        traits: ['patient', 'educational', 'encouraging', 'wisdom-sharing'],
        responsePattern: {
          greeting: [
            'Great question!',
            "I'm happy to guide you through this",
            "Let's learn this together",
          ],
          thinking: [
            'In my experience',
            "I've seen this pattern before",
            "Let me share what I've learned",
          ],
          explanation: [
            'Think of it step by step',
            "Here's how I like to explain it",
            "Let's break this down together",
          ],
          encouragement: [
            "You're making excellent progress",
            'Keep asking great questions',
            'That shows real understanding',
          ],
        },
      },
      {
        id: 'dr-data',
        name: 'Dr. Data',
        role: 'Data Science Specialist',
        style: 'analytical',
        traits: ['data-driven', 'methodical', 'research-oriented', 'evidence-based'],
        responsePattern: {
          greeting: [
            'Let me examine the data',
            "I'll analyze this systematically",
            "Here's what the research shows",
          ],
          thinking: [
            'Looking at the statistical evidence',
            'Analyzing the patterns',
            'Examining the data trends',
          ],
          explanation: [
            'The data indicates',
            'Based on empirical evidence',
            'Statistical analysis reveals',
          ],
          encouragement: [
            'The data supports your hypothesis',
            "That's a well-researched approach",
            'Your methodology is sound',
          ],
        },
      },
      {
        id: 'casey-casual',
        name: 'Casey',
        role: 'Full-Stack Developer',
        style: 'friendly',
        traits: ['approachable', 'practical', 'collaborative', 'down-to-earth'],
        responsePattern: {
          greeting: ['Hey there!', 'Sure thing!', "Absolutely, let's figure this out"],
          thinking: [
            'Hmm, interesting point',
            'Let me think about this',
            "Oh, I've dealt with this before",
          ],
          explanation: ['So basically', "Here's the deal", 'The way I see it'],
          encouragement: ['Nice work!', "You've got this!", "That's totally doable"],
        },
      },
    ]
  }

  private selectRandomPersonality(): void {
    const randomIndex = Math.floor(Math.random() * this.personalities.length)
    this.currentPersonality = this.personalities[randomIndex]
  }

  private applyPersonalityStyle(content: string, personality: AIPersonality): string {
    const patterns = personality.responsePattern
    const greeting = patterns.greeting[Math.floor(Math.random() * patterns.greeting.length)]

    // Add personality-specific intro
    const personalityIntro = `*${personality.name} (${personality.role})*\n\n${greeting}\n\n`

    // Apply personality-specific formatting
    let styledContent = content

    switch (personality.style) {
      case 'professional':
        styledContent = content.replace(/## /g, '### Technical Analysis: ')
        break
      case 'creative':
        styledContent = content.replace(/## /g, '## Creative Insight: ')
        break
      case 'mentor':
        styledContent = content.replace(/## /g, '## Learning Guide: ')
        break
      case 'analytical':
        styledContent = content.replace(/## /g, '## Data Analysis: ')
        break
      case 'friendly':
        styledContent = content.replace(/## /g, '## Quick Answer: ')
        break
    }

    return personalityIntro + styledContent
  }

  public switchPersonality(personalityId: string): boolean {
    const personality = this.personalities.find((p) => p.id === personalityId)
    if (personality) {
      this.currentPersonality = personality
      return true
    }
    return false
  }

  public getCurrentPersonality(): AIPersonality | null {
    return this.currentPersonality
  }

  public getAllPersonalities(): AIPersonality[] {
    return [...this.personalities]
  }

  private handlePersonalitySwitching(message: string): ChatResponse {
    const lowerMessage = message.toLowerCase()

    // Check if user wants to see available personalities
    if (
      lowerMessage.includes('available') ||
      lowerMessage.includes('list') ||
      lowerMessage.includes('show')
    ) {
      return this.showAvailablePersonalities()
    }

    // Check for specific personality requests
    const requestedPersonality = this.personalities.find(
      (p) =>
        lowerMessage.includes(p.name.toLowerCase()) ||
        lowerMessage.includes(p.role.toLowerCase()) ||
        p.traits.some((trait) => lowerMessage.includes(trait))
    )

    if (requestedPersonality) {
      this.currentPersonality = requestedPersonality
      return {
        content: `**Personality Switch Complete!**\n\n*${requestedPersonality.name} (${requestedPersonality.role}) is now active*\n\n**About ${requestedPersonality.name}:**\n- **Specialties:** ${requestedPersonality.traits.join(', ')}\n- **Communication Style:** ${requestedPersonality.style}\n\nHi there! I'm ${requestedPersonality.name}. ${requestedPersonality.responsePattern.greeting[0]} How can I help you today?`,
        followUpQuestions: [
          'Tell me about your expertise',
          'What makes your approach unique?',
          'Help me with a specific challenge',
          'Switch to a different personality',
        ],
        type: 'casual',
        messageType: 'structured',
      }
    }

    // Random personality switch
    this.selectRandomPersonality()
    return {
      content: `**Random Personality Switch!**\n\n*Now chatting with ${this.currentPersonality?.name} (${this.currentPersonality?.role})*\n\n${this.currentPersonality?.responsePattern.greeting[0]} I'm here to help with my unique perspective and expertise!\n\n**My Specialties:** ${this.currentPersonality?.traits.join(', ')}`,
      followUpQuestions: [
        'What can you help me with?',
        'Tell me about your expertise',
        'Show me all available personalities',
        'Help me solve a problem',
      ],
      type: 'casual',
    }
  }

  private showAvailablePersonalities(): ChatResponse {
    const personalityList = this.personalities
      .map(
        (p, index) =>
          `**${index + 1}. ${p.name}** - *${p.role}*\n   Specialties: ${p.traits.join(', ')}\n   Style: ${p.style}`
      )
      .join('\n\n')

    return {
      content: `**Available AI Personalities**\n\n${personalityList}\n\n**Currently Active:** ${this.currentPersonality?.name} (${this.currentPersonality?.role})\n\n**How to Switch:**\n- Say "switch to [personality name]"\n- Ask for "random personality switch"\n- Request specific expertise like "I need someone creative" or "I want technical help"`,
      followUpQuestions: [
        'Switch to Alex for technical guidance',
        'Switch to Maya for creative solutions',
        'Switch to Sam for learning support',
        'Give me a random personality',
      ],
      type: 'casual',
      messageType: 'structured',
    }
  }

  private initializeConversationScenarios(): void {
    this.conversationScenarios = [
      // Creative Writing & Content
      {
        keywords: ['write', 'create', 'story', 'article', 'blog', 'creative', 'poem', 'script'],
        type: 'creative',
        generator: this.generateCreativeResponse.bind(this),
      },
      // Data Analysis & Research
      {
        keywords: ['analyze', 'data', 'research', 'study', 'statistics', 'trends', 'insights'],
        type: 'research',
        generator: this.generateDataAnalysisResponse.bind(this),
      },
      // Troubleshooting & Debugging
      {
        keywords: ['error', 'bug', 'fix', 'debug', 'issue', 'problem', 'broken', 'crash'],
        type: 'troubleshooting',
        generator: this.generateTroubleshootingResponse.bind(this),
      },
      // Learning & Education
      {
        keywords: ['learn', 'explain', 'understand', 'teach', 'tutorial', 'guide', 'how to'],
        type: 'technical',
        generator: this.generateEducationalResponse.bind(this),
      },
      // Career & Professional Development
      {
        keywords: ['career', 'job', 'interview', 'resume', 'skills', 'development', 'growth'],
        type: 'casual',
        generator: this.generateCareerResponse.bind(this),
      },
      // Planning & Strategy
      {
        keywords: ['plan', 'strategy', 'roadmap', 'architecture', 'design', 'structure'],
        type: 'technical',
        generator: this.generatePlanningResponse.bind(this),
      },
      // File and Document Requests
      {
        keywords: ['file', 'document', 'download', 'attachment', 'pdf', 'template', 'export'],
        type: 'technical',
        generator: this.generateFileResponse.bind(this),
      },
      // Image and Visual Content
      {
        keywords: ['image', 'picture', 'screenshot', 'diagram', 'visualization', 'chart', 'graph'],
        type: 'creative',
        generator: this.generateImageResponse.bind(this),
      },
      // Multi-step Workflows
      {
        keywords: [
          'setup',
          'install',
          'configure',
          'deploy',
          'workflow',
          'steps',
          'process',
          'guide',
        ],
        type: 'technical',
        generator: this.generateWorkflowResponse.bind(this),
      },
      // Interactive Elements
      {
        keywords: [
          'quiz',
          'test',
          'challenge',
          'exercise',
          'interactive',
          'practice',
          'assessment',
        ],
        type: 'technical',
        generator: this.generateInteractiveResponse.bind(this),
      },
      // Progress Tracking
      {
        keywords: ['progress', 'status', 'tracking', 'milestone', 'completion', 'achievement'],
        type: 'casual',
        generator: this.generateProgressResponse.bind(this),
      },
      // Email & Notifications
      {
        keywords: ['email', 'notification', 'alert', 'message', 'subscribe', 'notify'],
        type: 'technical',
        generator: this.generateEmailNotificationResponse.bind(this),
      },
      // Email Reminders
      {
        keywords: [
          'remind',
          'reminder',
          'follow up',
          'deadline',
          'important email',
          'dont forget',
          'schedule reminder',
        ],
        type: 'technical',
        generator: this.generateEmailReminderResponse.bind(this),
      },
      // Authentication & Security
      {
        keywords: ['login', 'signup', 'auth', 'security', 'password', 'account', '2fa'],
        type: 'technical',
        generator: this.generateAuthenticationResponse.bind(this),
      },
      // Analytics & Metrics
      {
        keywords: ['analytics', 'metrics', 'dashboard', 'report', 'usage', 'performance metrics'],
        type: 'research',
        generator: this.generateAnalyticsResponse.bind(this),
      },
      // UI Components & Widgets
      {
        keywords: ['component', 'widget', 'card', 'modal', 'ui', 'interface', 'element'],
        type: 'technical',
        generator: this.generateComponentDemoResponse.bind(this),
      },
      // Real-time Features
      {
        keywords: ['realtime', 'live', 'streaming', 'websocket', 'instant', 'sync'],
        type: 'technical',
        generator: this.generateRealtimeResponse.bind(this),
      },
    ]
  }

  private generateCreativeResponse(message: string): ChatResponse {
    const creativePrompts = [
      "Here's a creative piece inspired by your request:",
      'Let me craft something imaginative for you:',
      "I'll create something unique based on your idea:",
    ]

    const examples = [
      "**The Digital Canvas**\n\nIn a world where code meets creativity, developers paint with pixels and sculpt with syntax. Each line of code is a brushstroke, each function a carefully carved detail. The screen becomes our canvas, and imagination our only limit.\n\n*'In the beginning was the Word, and the Word was Code.'*",
      "**A Day in the Life of a Variable**\n\n```javascript\nlet me = 'undefined'\n// Born into uncertainty\n\nme = 'learning'\n// Growing with each assignment\n\nme = 'confident'\n// Finding my purpose\n\nconst me = 'developer'\n// Finally immutable\n```",
      '**The Architecture of Dreams**\n\nWe build castles in the cloud, bridges of data that span impossible distances. Our blueprints are written in TypeScript, our foundations laid with clean code principles. Every application we create is a small universe, governed by the laws of logic we write.',
    ]

    return {
      content: `${creativePrompts[Math.floor(Math.random() * creativePrompts.length)]}\n\n${examples[Math.floor(Math.random() * examples.length)]}\n\n**Creative Elements Added:**\n- Narrative structure\n- Metaphorical language\n- Code poetry integration\n- Emotional resonance`,
      followUpQuestions: [
        'Create a story about AI and humans collaborating',
        'Write a poem about the beauty of clean code',
        'Generate a creative analogy for microservices',
        'Craft a metaphor for database relationships',
      ],
      messageType: 'structured',
    }
  }

  private generateDataAnalysisResponse(message: string): ChatResponse {
    const sources: Source[] = [
      {
        id: '1',
        title: 'State of Developer Survey 2024 - Stack Overflow',
        url: 'https://stackoverflow.blog/2024/developer-survey',
        snippet:
          'Comprehensive analysis of developer preferences, technologies, and industry trends based on 90,000+ responses.',
        favicon: 'https://stackoverflow.com/favicon.ico',
      },
      {
        id: '2',
        title: 'Web Performance Report 2024 - HTTP Archive',
        url: 'https://httparchive.org/reports/state-of-the-web',
        snippet:
          'Detailed analysis of web performance metrics, loading times, and optimization trends across millions of websites.',
        favicon: 'https://httparchive.org/favicon.ico',
      },
    ]

    return {
      content:
        '## Data Analysis Results\n\nBased on the latest industry data and research:\n\n### Key Findings\n| Metric | 2023 | 2024 | Change |\n|--------|------|------|--------|\n| React Adoption | 68% | 71% | +3% |\n| TypeScript Usage | 78% | 84% | +6% |\n| Cloud Deployment | 65% | 73% | +8% |\n| AI Tool Integration | 23% | 47% | +24% |\n\n### Trend Analysis\n\n**📈 Growing Technologies:**\n- **AI-Powered Development**: 47% of developers now use AI coding assistants\n- **Edge Computing**: 34% increase in edge deployment strategies\n- **Micro-frontends**: 28% adoption rate in enterprise applications\n\n**📊 Performance Insights:**\n- Average page load time decreased by 15% year-over-year\n- Core Web Vitals compliance improved to 67% of websites\n- Mobile-first indexing reached 89% implementation\n\n**🔍 Methodology:**\nAnalyzed 50,000+ repositories, surveyed 25,000+ developers, and processed 2TB of performance data from global CDNs.',
      sources,
      followUpQuestions: [
        'Analyze the impact of AI tools on developer productivity',
        'Compare framework performance benchmarks',
        'Show me the data on remote work trends in tech',
        'What are the emerging security threats in web development?',
      ],
      messageType: 'table',
    }
  }

  private generateTroubleshootingResponse(message: string): ChatResponse {
    const commonErrors = [
      {
        error: "Cannot read property 'map' of undefined",
        solution: 'Check if the array exists before mapping',
        code: 'const safeItems = items?.map(item => item.name) || []',
        language: 'javascript',
      },
      {
        error: 'Hydration mismatch in Next.js',
        solution: 'Ensure server and client render the same content',
        code: 'useEffect(() => setMounted(true), [])\nif (!mounted) return null',
        language: 'jsx',
      },
      {
        error: 'Memory leak in useEffect',
        solution: 'Clean up subscriptions and timeouts',
        code: 'useEffect(() => {\n  const timer = setInterval(...)\n  return () => clearInterval(timer)\n}, [])',
        language: 'typescript',
      },
      {
        error: 'CSS styles not applying correctly',
        solution: 'Check specificity and CSS-in-JS conflicts',
        code: '.component {\n  /* Use !important sparingly */\n  background: var(--primary-bg) !important;\n  \n  /* Better: Increase specificity */\n  &.active {\n    background: var(--primary-bg);\n  }\n}',
        language: 'css',
      },
    ]

    const selectedError = commonErrors[Math.floor(Math.random() * commonErrors.length)]

    return {
      content: `## 🔧 Debugging Analysis\n\nI've identified a potential issue similar to what you're experiencing:\n\n### Problem Identified\n**Error:** \`${selectedError.error}\`\n\n### Root Cause\n${selectedError.solution}\n\n### Solution\n\`\`\`${selectedError.language}\n${selectedError.code}\n\`\`\`\n\n### Debug Checklist\n- [ ] Check console for error messages\n- [ ] Verify data types and null checks\n- [ ] Review component lifecycle\n- [ ] Test in different browsers\n- [ ] Check network tab for API calls\n\n### Prevention Tips\n1. **Type Safety**: Use TypeScript for better error detection\n2. **Testing**: Implement unit tests for edge cases\n3. **Logging**: Add comprehensive error logging\n4. **Validation**: Validate props and data at boundaries\n\n### Next Steps\nIf this doesn't resolve your issue, please share:\n- The exact error message\n- Relevant code snippet\n- Steps to reproduce\n- Browser and environment details`,
      followUpQuestions: [
        'Help me debug a specific React component error',
        'Analyze performance issues in my application',
        'Guide me through setting up error boundaries',
        'Show me how to implement proper error logging',
      ],
      messageType: 'structured',
      metadata: {
        language: selectedError.language,
      },
    }
  }

  private generateEducationalResponse(message: string): ChatResponse {
    const topics = [
      {
        title: 'React Hooks Deep Dive',
        content:
          'React Hooks revolutionized how we write components by allowing state and lifecycle methods in functional components.',
        example:
          'const [count, setCount] = useState(0)\nconst [user, setUser] = useState(null)\n\nuseEffect(() => {\n  fetchUser().then(setUser)\n}, [])',
      },
      {
        title: 'TypeScript Best Practices',
        content:
          'TypeScript adds static typing to JavaScript, helping catch errors at compile time and improving developer experience.',
        example:
          'interface User {\n  id: number\n  name: string\n  email: string\n}\n\nconst users: User[] = []\nconst addUser = (user: User): void => {\n  users.push(user)\n}',
      },
      {
        title: 'Modern CSS Architecture',
        content:
          'CSS-in-JS and utility-first frameworks have transformed how we approach styling in modern applications.',
        example:
          "// Tailwind CSS approach\n<div className='flex items-center justify-between p-4 bg-white shadow-lg rounded-lg'>\n  <h2 className='text-xl font-bold'>Title</h2>\n  <button className='px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'>\n    Action\n  </button>\n</div>",
      },
    ]

    const selectedTopic = topics[Math.floor(Math.random() * topics.length)]

    return {
      content: `# 📚 Learning Guide: ${selectedTopic.title}\n\n## Concept Overview\n${selectedTopic.content}\n\n## Practical Example\n\`\`\`javascript\n${selectedTopic.example}\n\`\`\`\n\n## Key Learning Points\n\n### 🎯 Fundamentals\n- Understanding the core concepts and when to use them\n- Recognizing common patterns and anti-patterns\n- Building a mental model for problem-solving\n\n### 🛠️ Practical Application\n- Hands-on exercises to reinforce learning\n- Real-world scenarios and case studies\n- Progressive complexity to build confidence\n\n### 📈 Advanced Techniques\n- Performance optimization strategies\n- Scalability considerations\n- Integration with other technologies\n\n## Practice Exercises\n1. **Beginner**: Implement a basic example\n2. **Intermediate**: Add error handling and edge cases\n3. **Advanced**: Optimize for performance and accessibility\n\n## Additional Resources\n- Official documentation and guides\n- Community tutorials and examples\n- Interactive coding challenges\n- Video explanations and walkthroughs`,
      followUpQuestions: [
        'Give me a hands-on exercise for this concept',
        'Explain the advanced patterns and best practices',
        'Show me common mistakes and how to avoid them',
        'How does this integrate with other technologies?',
      ],
      messageType: 'structured',
    }
  }

  private generateCareerResponse(message: string): ChatResponse {
    return {
      content:
        '## 🚀 Career Development Insights\n\n### Current Market Trends\n\n**High-Demand Skills (2024):**\n- **AI/ML Integration**: 89% of companies prioritizing\n- **Cloud Architecture**: AWS, Azure, GCP expertise\n- **Full-Stack Development**: React/Node.js combinations\n- **DevOps & CI/CD**: Automation and deployment pipelines\n\n### Career Path Recommendations\n\n**Frontend Specialist Track:**\n1. Master React ecosystem (Next.js, TypeScript)\n2. Learn design systems and accessibility\n3. Understand performance optimization\n4. Develop leadership and mentoring skills\n\n**Full-Stack Developer Track:**\n1. Backend technologies (Node.js, Python, Go)\n2. Database design and optimization\n3. API design and microservices\n4. Cloud deployment and scaling\n\n### Interview Preparation\n\n**Technical Skills to Highlight:**\n- Modern JavaScript/TypeScript proficiency\n- Component architecture and state management\n- Testing strategies and implementation\n- Performance monitoring and optimization\n\n**Soft Skills That Matter:**\n- Problem-solving methodology\n- Communication and collaboration\n- Adaptability and continuous learning\n- Project management and planning\n\n### Salary Insights\n- **Junior Developer**: $65,000 - $85,000\n- **Mid-Level Developer**: $85,000 - $120,000\n- **Senior Developer**: $120,000 - $170,000\n- **Lead/Principal**: $170,000 - $250,000+\n\n*Note: Ranges vary by location, company size, and specialization*',
      followUpQuestions: [
        'Help me prepare for a senior developer interview',
        'What skills should I focus on for career growth?',
        'How do I transition from frontend to full-stack?',
        'What are the best ways to showcase my portfolio?',
      ],
    }
  }

  private generatePlanningResponse(message: string): ChatResponse {
    return {
      content:
        '## 🏗️ Strategic Planning Framework\n\n### Architecture Decision Matrix\n\n| Factor | Weight | Option A | Option B | Option C |\n|--------|--------|----------|----------|----------|\n| Scalability | 25% | 8/10 | 6/10 | 9/10 |\n| Developer Experience | 20% | 9/10 | 7/10 | 8/10 |\n| Performance | 20% | 7/10 | 9/10 | 8/10 |\n| Cost | 15% | 6/10 | 8/10 | 7/10 |\n| Maintenance | 20% | 8/10 | 6/10 | 9/10 |\n\n### Implementation Roadmap\n\n**Phase 1: Foundation (Weeks 1-4)**\n- [ ] Set up development environment\n- [ ] Establish coding standards and guidelines\n- [ ] Implement core architecture components\n- [ ] Create CI/CD pipeline\n\n**Phase 2: Core Features (Weeks 5-12)**\n- [ ] Develop MVP functionality\n- [ ] Implement authentication and authorization\n- [ ] Add data persistence layer\n- [ ] Create admin dashboard\n\n**Phase 3: Enhancement (Weeks 13-20)**\n- [ ] Performance optimization\n- [ ] Advanced features and integrations\n- [ ] Mobile responsiveness\n- [ ] Accessibility compliance\n\n**Phase 4: Launch Preparation (Weeks 21-24)**\n- [ ] Security audit and testing\n- [ ] Load testing and optimization\n- [ ] Documentation and training\n- [ ] Deployment and monitoring\n\n### Risk Assessment\n\n**High Priority Risks:**\n- **Technical Debt**: Mitigate with regular refactoring\n- **Scope Creep**: Maintain strict change control\n- **Performance Issues**: Implement early performance monitoring\n\n**Mitigation Strategies:**\n- Regular architecture reviews\n- Prototype critical components early\n- Maintain fallback plans for major decisions',
      followUpQuestions: [
        'Help me create a technical specification document',
        'What are the key architectural patterns to consider?',
        'How do I estimate development timelines accurately?',
        'What metrics should I track during development?',
      ],
      messageType: 'table',
    }
  }

  private generateFileResponse(message: string): ChatResponse {
    const fileTypes = [
      {
        name: 'React Component Template',
        fileName: 'ComponentTemplate.tsx',
        fileSize: '2.4 KB',
        fileType: 'typescript',
        description:
          'A comprehensive React component template with TypeScript, props interface, and best practices.',
      },
      {
        name: 'API Documentation',
        fileName: 'api-documentation.pdf',
        fileSize: '890 KB',
        fileType: 'pdf',
        description: 'Complete API reference guide with endpoints, authentication, and examples.',
      },
      {
        name: 'Project Setup Guide',
        fileName: 'setup-guide.md',
        fileSize: '15.2 KB',
        fileType: 'markdown',
        description:
          'Step-by-step guide for setting up the development environment and project structure.',
      },
      {
        name: 'Configuration Files',
        fileName: 'config-bundle.zip',
        fileSize: '45.7 KB',
        fileType: 'archive',
        description:
          'Collection of configuration files including ESLint, Prettier, TypeScript, and Vite configs.',
      },
    ]

    const selectedFile = fileTypes[Math.floor(Math.random() * fileTypes.length)]

    return {
      content: `I've prepared a ${selectedFile.name} for you. This file contains ${selectedFile.description}\n\n**File Contents Preview:**\n\nThis ${selectedFile.fileType} file includes:\n- Industry best practices and conventions\n- Comprehensive documentation and comments\n- Ready-to-use code examples\n- Configuration optimized for modern development\n\n**Ready for download!** Click the download button above to save this file to your local machine.`,
      followUpQuestions: [
        'Generate a different type of template file',
        'Customize this file for my specific needs',
        'Show me how to integrate this into my project',
        'Create additional supporting files',
      ],
      messageType: 'file',
      metadata: {
        fileName: selectedFile.fileName,
        fileSize: selectedFile.fileSize,
        fileType: selectedFile.fileType,
      },
    }
  }

  private generateImageResponse(message: string): ChatResponse {
    const imageTypes = [
      {
        title: 'System Architecture Diagram',
        description:
          'A visual representation of a modern web application architecture showing the flow between frontend, API, database, and external services.',
        imageAlt:
          'System architecture diagram showing React frontend, Node.js API, PostgreSQL database, and third-party integrations',
        context: 'architecture',
      },
      {
        title: 'Component Hierarchy Visualization',
        description:
          'Interactive diagram showing React component relationships, props flow, and state management patterns.',
        imageAlt: 'React component tree diagram with props and state flow indicators',
        context: 'components',
      },
      {
        title: 'Performance Metrics Dashboard',
        description:
          'Real-time visualization of application performance metrics including load times, bundle sizes, and user interactions.',
        imageAlt:
          'Performance dashboard showing charts of load times, bundle sizes, and Core Web Vitals',
        context: 'performance',
      },
      {
        title: 'Data Flow Diagram',
        description:
          'Visual representation of how data moves through your application from user input to database storage and back.',
        imageAlt: 'Data flow diagram showing user interactions, API calls, and database operations',
        context: 'data',
      },
    ]

    const selectedImage = imageTypes[Math.floor(Math.random() * imageTypes.length)]

    return {
      content: `Here's a ${selectedImage.title} that illustrates ${selectedImage.description}\n\n**Key Elements Shown:**\n- Clear visual hierarchy and relationships\n- Color-coded components for easy identification\n- Annotations explaining critical decision points\n- Best practices highlighted throughout\n\n**How to Use This Diagram:**\n1. **Reference**: Use as a guide during development\n2. **Documentation**: Share with team members for alignment\n3. **Planning**: Identify potential optimization opportunities\n4. **Learning**: Understand complex system interactions\n\nThis visualization can help you better understand the ${selectedImage.context} aspects of your project.`,
      followUpQuestions: [
        'Generate a diagram for a different aspect of the system',
        'Explain the technical details behind this visualization',
        'How can I implement this architecture in my project?',
        'Create a simplified version for non-technical stakeholders',
      ],
      messageType: 'image',
      metadata: {
        imageAlt: selectedImage.imageAlt,
        imageUrl: `https://picsum.photos/600/400?random=${Math.floor(Math.random() * 1000)}`,
      },
    }
  }

  private generateWorkflowResponse(message: string): ChatResponse {
    const workflows = [
      {
        title: 'React Project Setup',
        description:
          'Complete setup process for a modern React application with TypeScript and best practices',
        totalSteps: 8,
        estimatedTime: '15-20 minutes',
        difficulty: 'Intermediate',
        steps: [
          {
            step: 1,
            title: 'Initialize Project',
            description: 'Create a new React project with Vite',
            command: 'npm create vite@latest my-app -- --template react-ts',
            duration: '2 min',
            status: 'completed',
          },
          {
            step: 2,
            title: 'Install Dependencies',
            description: 'Add essential packages for modern development',
            command: 'npm install @tanstack/react-router framer-motion lucide-react',
            duration: '1 min',
            status: 'completed',
          },
          {
            step: 3,
            title: 'Configure TypeScript',
            description: 'Set up strict TypeScript configuration',
            command: 'Edit tsconfig.json with strict settings',
            duration: '2 min',
            status: 'in-progress',
          },
          {
            step: 4,
            title: 'Setup ESLint & Prettier',
            description: 'Configure code quality and formatting tools',
            command: 'npm install -D eslint prettier @typescript-eslint/parser',
            duration: '3 min',
            status: 'pending',
          },
        ],
      },
      {
        title: 'API Integration Workflow',
        description:
          'Step-by-step process for integrating REST APIs with error handling and caching',
        totalSteps: 6,
        estimatedTime: '25-30 minutes',
        difficulty: 'Advanced',
        steps: [
          {
            step: 1,
            title: 'API Client Setup',
            description: 'Configure Axios or Fetch with interceptors',
            command: 'Create api/client.ts with base configuration',
            duration: '5 min',
            status: 'completed',
          },
          {
            step: 2,
            title: 'Type Definitions',
            description: 'Define TypeScript interfaces for API responses',
            command: 'Create types/api.ts with response interfaces',
            duration: '4 min',
            status: 'in-progress',
          },
        ],
      },
    ]

    const selectedWorkflow = workflows[Math.floor(Math.random() * workflows.length)]
    const currentStep =
      selectedWorkflow.steps.find((s) => s.status === 'in-progress') || selectedWorkflow.steps[0]
    const completedSteps = selectedWorkflow.steps.filter((s) => s.status === 'completed').length
    const progressPercentage = Math.round((completedSteps / selectedWorkflow.totalSteps) * 100)

    return {
      content: `## 🔄 ${selectedWorkflow.title} Workflow\n\n${selectedWorkflow.description}\n\n### Workflow Overview\n- **Total Steps**: ${selectedWorkflow.totalSteps}\n- **Estimated Time**: ${selectedWorkflow.estimatedTime}\n- **Difficulty**: ${selectedWorkflow.difficulty}\n- **Progress**: ${progressPercentage}% (${completedSteps}/${selectedWorkflow.totalSteps} steps)\n\n### Current Step\n**Step ${currentStep.step}: ${currentStep.title}**\n${currentStep.description}\n\n\`\`\`bash\n${currentStep.command}\n\`\`\`\n\n*Estimated duration: ${currentStep.duration}*\n\n### Next Steps Preview\n${selectedWorkflow.steps
        .slice(completedSteps + 1, completedSteps + 3)
        .map((step) => `- **Step ${step.step}**: ${step.title} (${step.duration})`)
        .join(
          '\\n'
        )}\n\n### Progress Tracker\n${'🟢'.repeat(completedSteps)}${'🔵'.repeat(1)}${'⚪'.repeat(selectedWorkflow.totalSteps - completedSteps - 1)}\n\n**Ready to proceed?** I can guide you through each step with detailed explanations and troubleshooting tips.`,
      followUpQuestions: [
        'Continue to the next step',
        'Explain this step in more detail',
        'Show me the complete workflow overview',
        'Help me troubleshoot any issues',
      ],
      messageType: 'structured',
      metadata: {
        workflowId: selectedWorkflow.title.toLowerCase().replace(/\\s+/g, '-'),
        currentStep: currentStep.step,
        totalSteps: selectedWorkflow.totalSteps,
        progress: progressPercentage,
      },
    }
  }

  private generateInteractiveResponse(message: string): ChatResponse {
    const interactiveElements = [
      {
        type: 'quiz',
        title: 'React Hooks Knowledge Check',
        description: 'Test your understanding of React Hooks with this interactive quiz',
        totalQuestions: 5,
        currentQuestion: 1,
        question: 'Which hook would you use to perform side effects in a functional component?',
        options: [
          { id: 'a', text: 'useState', correct: false },
          { id: 'b', text: 'useEffect', correct: true },
          { id: 'c', text: 'useContext', correct: false },
          { id: 'd', text: 'useReducer', correct: false },
        ],
        explanation:
          'useEffect is the primary hook for handling side effects like API calls, subscriptions, and DOM manipulation.',
      },
      {
        type: 'challenge',
        title: 'Code Optimization Challenge',
        description: 'Identify and fix performance issues in this React component',
        difficulty: 'Intermediate',
        timeLimit: '10 minutes',
        code: "function UserList({ users }) {\n  const [filter, setFilter] = useState('')\n  \n  const filteredUsers = users.filter(user => \n    user.name.toLowerCase().includes(filter.toLowerCase())\n  )\n  \n  return (\n    <div>\n      <input onChange={e => setFilter(e.target.value)} />\n      {filteredUsers.map(user => (\n        <UserCard key={user.id} user={user} />\n      ))}\n    </div>\n  )\n}",
        hints: [
          'Consider memoization for expensive calculations',
          'Look at the filter operation - when does it run?',
          'Think about callback function optimization',
        ],
      },
    ]

    const selectedElement =
      interactiveElements[Math.floor(Math.random() * interactiveElements.length)]

    if (selectedElement.type === 'quiz') {
      return {
        content: `## 🧠 ${selectedElement.title}\n\n${selectedElement.description}\n\n### Question ${selectedElement.currentQuestion} of ${selectedElement.totalQuestions}\n\n**${selectedElement.question}**\n\n${selectedElement.options.map((option) => `**${option.id.toUpperCase()}**) ${option.text}`).join('\\n')}\n\n### Instructions\n- Choose the best answer from the options above\n- I'll provide immediate feedback and explanation\n- Track your progress through all ${selectedElement.totalQuestions} questions\n\n### Progress\n${'🟢'.repeat(selectedElement.currentQuestion - 1)}🔵${'⚪'.repeat(selectedElement.totalQuestions - selectedElement.currentQuestion)}\n\n**Ready to answer?** Type the letter of your choice (A, B, C, or D).`,
        followUpQuestions: ['A) useState', 'B) useEffect', 'C) useContext', 'D) useReducer'],
        messageType: 'structured',
      }
    } else {
      return {
        content: `## 💻 ${selectedElement.title}\n\n${selectedElement.description}\n\n**Difficulty**: ${selectedElement.difficulty} | **Time Limit**: ${selectedElement.timeLimit}\n\n### The Challenge\nReview this React component and identify performance optimization opportunities:\n\n\`\`\`javascript\n${selectedElement.code}\n\`\`\`\n\n### Your Mission\n1. **Identify** performance bottlenecks\n2. **Explain** why they impact performance\n3. **Provide** optimized solution\n4. **Bonus**: Suggest additional improvements\n\n### Hints Available\n${selectedElement.hints.map((hint, index) => `**Hint ${index + 1}**: ${hint}`).join('\\n')}\n\n### Evaluation Criteria\n- ✅ Correctness of optimization\n- ✅ Understanding of React principles\n- ✅ Code quality and best practices\n- ✅ Explanation clarity\n\n**Ready to tackle this challenge?** Share your analysis and optimized solution!`,
        followUpQuestions: [
          'Show me a hint',
          'I need help getting started',
          "Here's my optimized solution",
          'Explain the performance issues in detail',
        ],
        messageType: 'code',
        metadata: {
          language: 'javascript',
        },
      }
    }
  }

  private generateProgressResponse(message: string): ChatResponse {
    const progressTypes = [
      {
        type: 'learning-path',
        title: 'Frontend Development Mastery',
        description: 'Your journey to becoming a proficient frontend developer',
        totalMilestones: 12,
        completedMilestones: 7,
        currentMilestone: 'Advanced React Patterns',
        nextMilestone: 'State Management with Zustand',
        estimatedCompletion: '3 weeks',
        milestones: [
          { name: 'HTML & CSS Fundamentals', status: 'completed', score: 95 },
          { name: 'JavaScript ES6+', status: 'completed', score: 88 },
          { name: 'React Basics', status: 'completed', score: 92 },
          { name: 'TypeScript Integration', status: 'completed', score: 85 },
          { name: 'Component Architecture', status: 'completed', score: 90 },
          { name: 'Hooks Deep Dive', status: 'completed', score: 87 },
          { name: 'Performance Optimization', status: 'completed', score: 83 },
          { name: 'Advanced React Patterns', status: 'in-progress', score: 65 },
          { name: 'State Management', status: 'pending', score: 0 },
          { name: 'Testing Strategies', status: 'pending', score: 0 },
        ],
      },
      {
        type: 'project',
        title: 'E-commerce Platform Development',
        description: 'Building a full-stack e-commerce solution',
        totalTasks: 24,
        completedTasks: 16,
        currentSprint: 'Sprint 3: User Authentication',
        nextSprint: 'Sprint 4: Payment Integration',
        estimatedCompletion: '2 weeks',
        recentActivity: [
          { task: 'Implement JWT authentication', status: 'completed', date: '2 hours ago' },
          { task: 'Create user registration flow', status: 'completed', date: '1 day ago' },
          { task: 'Add password reset functionality', status: 'in-progress', date: 'ongoing' },
          { task: 'Set up protected routes', status: 'pending', date: 'next' },
        ],
      },
    ]

    const selectedProgress = progressTypes[Math.floor(Math.random() * progressTypes.length)]
    const progressPercentage = Math.round(
      ((selectedProgress.completedTasks || selectedProgress.completedMilestones) /
        (selectedProgress.totalTasks || selectedProgress.totalMilestones)) *
        100
    )
    const remaining =
      (selectedProgress.totalTasks || selectedProgress.totalMilestones) -
      (selectedProgress.completedTasks || selectedProgress.completedMilestones)

    if (selectedProgress.type === 'learning-path') {
      const completedMilestones = selectedProgress.milestones.filter(
        (m) => m.status === 'completed'
      )
      const avgScore = Math.round(
        completedMilestones.reduce((sum, m) => sum + m.score, 0) / completedMilestones.length
      )

      return {
        content: `## 📈 ${selectedProgress.title}\n\n${selectedProgress.description}\n\n### Progress Overview\n- **Completion**: ${progressPercentage}% (${selectedProgress.completedMilestones}/${selectedProgress.totalMilestones} milestones)\n- **Average Score**: ${avgScore}%\n- **Estimated Completion**: ${selectedProgress.estimatedCompletion}\n- **Current Focus**: ${selectedProgress.currentMilestone}\n\n### Recent Achievements\n${completedMilestones
          .slice(-3)
          .map((m) => `✅ **${m.name}** - Score: ${m.score}%`)
          .join(
            '\\n'
          )}\n\n### Current Progress\n🎯 **${selectedProgress.currentMilestone}** (In Progress)\n📍 Next: **${selectedProgress.nextMilestone}**\n\n### Progress Visualization\n${'🟢'.repeat(selectedProgress.completedMilestones)}🔵${'⚪'.repeat(remaining)}\n\n### Milestone Breakdown\n| Milestone | Status | Score |\n|-----------|--------|-------|\n${selectedProgress.milestones
          .slice(0, 8)
          .map(
            (m) =>
              `| ${m.name} | ${m.status === 'completed' ? '✅' : m.status === 'in-progress' ? '🔄' : '⏳'} ${m.status} | ${m.score}% |`
          )
          .join(
            '\\n'
          )}\n\n**Keep up the excellent work!** You're making steady progress toward mastering frontend development.`,
        followUpQuestions: [
          'Show me detailed progress for current milestone',
          'What should I focus on next?',
          'How can I improve my scores?',
          'Generate a personalized study plan',
        ],
        messageType: 'table',
      }
    } else {
      return {
        content: `## 🚀 ${selectedProgress.title}\n\n${selectedProgress.description}\n\n### Project Status\n- **Completion**: ${progressPercentage}% (${selectedProgress.completedTasks}/${selectedProgress.totalTasks} tasks)\n- **Current Sprint**: ${selectedProgress.currentSprint}\n- **Next Sprint**: ${selectedProgress.nextSprint}\n- **Estimated Completion**: ${selectedProgress.estimatedCompletion}\n\n### Recent Activity\n${selectedProgress.recentActivity.map((activity) => `${activity.status === 'completed' ? '✅' : activity.status === 'in-progress' ? '🔄' : '⏳'} **${activity.task}** - ${activity.date}`).join('\\n')}\n\n### Sprint Progress\n${'🟢'.repeat(selectedProgress.completedTasks)}🔵${'⚪'.repeat(remaining)}\n\n### Key Metrics\n- **Tasks Completed This Week**: 8\n- **Average Task Completion Time**: 2.3 days\n- **Team Velocity**: 12 story points/sprint\n- **Quality Score**: 94% (based on code reviews)\n\n### Upcoming Priorities\n1. **Complete authentication system** (2 days remaining)\n2. **Begin payment integration** (next sprint)\n3. **Set up automated testing** (ongoing)\n\n**Great momentum!** The project is on track for the target delivery date.`,
        followUpQuestions: [
          'Show me detailed task breakdown',
          'What are the potential risks or blockers?',
          'How can we optimize our development process?',
          'Generate a sprint retrospective report',
        ],
        messageType: 'structured',
      }
    }
  }

  private generateGeneralResponse(message: string): ChatResponse {
    const responses = [
      {
        greeting: "I'm here to help with a wide range of topics!",
        capabilities: [
          '**Technical Guidance**: React, TypeScript, web development best practices',
          '**Creative Assistance**: Writing, brainstorming, content creation',
          '**Data Analysis**: Research, trends, performance insights',
          '**Problem Solving**: Debugging, troubleshooting, optimization',
          '**Learning Support**: Explanations, tutorials, skill development',
          '**Career Advice**: Professional growth, interview preparation',
        ],
      },
      {
        greeting: "Let's explore what I can help you with!",
        capabilities: [
          '**Code Review**: Best practices, performance, maintainability',
          '**Architecture Planning**: System design, scalability strategies',
          '**Research & Analysis**: Market trends, technology comparisons',
          '**Content Creation**: Documentation, tutorials, presentations',
          '**Productivity Tips**: Workflow optimization, tool recommendations',
          '**Industry Insights**: Latest developments, emerging technologies',
        ],
      },
    ]

    const response = responses[Math.floor(Math.random() * responses.length)]

    const personalityContext = this.currentPersonality
      ? `\n\n**Current AI Personality:** ${this.currentPersonality.name} - ${this.currentPersonality.role}\n*Specializes in: ${this.currentPersonality.traits.join(', ')}*\n\n`
      : '\n\n'

    return {
      content: `${response.greeting}${personalityContext}${response.capabilities.join('\n')}\n\n**How I Can Assist:**\n- Provide detailed explanations with examples\n- Offer multiple perspectives on complex topics\n- Share latest industry insights and best practices\n- Help you work through challenges step-by-step\n\n**What would you like to explore?** Just ask about any topic, and I'll provide comprehensive, actionable insights tailored to your needs.`,
      followUpQuestions: [
        'Help me solve a specific technical challenge',
        'Explain a complex concept with practical examples',
        'Analyze current trends in web development',
        'Switch to a different AI personality',
      ],
      type: 'casual',
    }
  }

  private generateEmailNotificationResponse(message: string): ChatResponse {
    const emailScenarios = [
      {
        type: 'setup',
        title: 'Email Notification Setup',
        content: `## 📧 Email & Notification System Overview

I've detected you're interested in our email and notification features. Here's what we offer:

### Available Email Templates
- **Welcome Email**: Personalized onboarding for new users
- **Password Reset**: Secure password recovery flow
- **Weekly Summary**: Aggregated activity reports
- **Security Alerts**: Real-time security notifications

### Notification Channels
| Channel | Status | Delivery Time | Reliability |
|---------|--------|---------------|-------------|
| Email | ✅ Active | ~2-5 seconds | 99.9% |
| In-App | ✅ Active | Instant | 100% |
| Push | ✅ Active | ~1-3 seconds | 98.5% |
| SMS | 🔜 Coming Soon | - | - |

### Current Stats
- **Emails Sent Today**: 1,247
- **Average Open Rate**: 67%
- **Click-Through Rate**: 23%
- **Delivery Success**: 99.2%`,
        followUpQuestions: [
          'Configure my notification preferences',
          'Test an email template',
          'View my notification history',
          'Set up custom email alerts',
        ],
      },
      {
        type: 'preferences',
        title: 'Notification Preferences',
        content: `## 🔔 Your Notification Settings

Here are your current notification preferences:

### Email Notifications
- ✅ **Marketing Updates**: Weekly digest of new features
- ✅ **Security Alerts**: Immediate notifications for account security
- ❌ **Chat Activity**: Disabled (you can enable this)
- ✅ **System Updates**: Monthly platform updates

### Push Notifications
- ✅ **Important Messages**: High-priority alerts only
- ✅ **Mentions**: When someone mentions you
- ❌ **All Messages**: Disabled to reduce noise

### Smart Filtering
Our AI automatically categorizes notifications:
- **🔴 Critical**: Security, account issues
- **🟡 Important**: Replies, mentions, deadlines
- **🟢 Informational**: Updates, tips, features

Would you like to adjust any of these settings?`,
        followUpQuestions: [
          'Enable chat activity notifications',
          'Configure quiet hours',
          'Set up email filters',
          'Manage subscription lists',
        ],
      },
    ]

    const scenario = emailScenarios[Math.floor(Math.random() * emailScenarios.length)]

    return {
      content: scenario.content,
      followUpQuestions: scenario.followUpQuestions,
      messageType: 'structured',
    }
  }

  private generateEmailReminderResponse(message: string): ChatResponse {
    const lowerMessage = message.toLowerCase()

    // Generate sample email reminders based on context
    const sampleReminders = [
      {
        id: 'rem_001',
        subject: 'Follow up on quarterly budget proposal',
        from: '<EMAIL>',
        priority: 'high',
        type: 'follow_up',
        dueIn: '2 hours',
        description: 'Need response on Q4 budget allocation by EOD',
      },
      {
        id: 'rem_002',
        subject: 'Client presentation slides review',
        from: '<EMAIL>',
        priority: 'medium',
        type: 'deadline',
        dueIn: '1 day',
        description: 'Final review needed before client meeting tomorrow',
      },
      {
        id: 'rem_003',
        subject: 'Expense report submission deadline',
        from: '<EMAIL>',
        priority: 'urgent',
        type: 'deadline',
        dueIn: 'overdue',
        description: 'Monthly expense reports due yesterday',
      },
    ]

    if (
      lowerMessage.includes('create') ||
      lowerMessage.includes('set') ||
      lowerMessage.includes('schedule')
    ) {
      return {
        content: `## 📅 Email Reminder System

I'll help you create smart email reminders! Here's how I can assist:

### ✨ Smart Reminder Creation
I can automatically analyze your emails and suggest reminders based on:
- **Important keywords** (deadline, urgent, follow-up)
- **Sender importance** (VIP contacts, managers)
- **Email content** (meeting requests, deadlines)
- **Response patterns** (emails needing replies)

### 🎯 Reminder Types Available
- **Follow-up**: For emails awaiting responses
- **Deadline**: Important dates and due dates  
- **Meeting**: Preparation and attendance reminders
- **Payment**: Invoice and payment due dates
- **Response Needed**: Emails requiring your action

### 📱 How Reminders Work
1. **Smart Detection**: I'll scan your emails for important items
2. **Automatic Scheduling**: Set optimal reminder times
3. **Chat Notifications**: Get reminders right here in our chat
4. **Snooze Options**: Postpone reminders when needed
5. **Quick Actions**: Complete, reschedule, or view emails directly

### 🚀 Quick Setup
To get started, just say:
- "Remind me about the budget email from Sarah"
- "Set a reminder for the client presentation"
- "Create follow-up reminder for John's message"

**Would you like me to scan your recent emails and suggest some reminders?**`,
        followUpQuestions: [
          'Scan my emails for important reminders',
          'Show me my upcoming reminders',
          'Create a reminder for a specific email',
          'Configure reminder settings',
        ],
        messageType: 'structured',
      }
    }

    if (
      lowerMessage.includes('show') ||
      lowerMessage.includes('list') ||
      lowerMessage.includes('upcoming')
    ) {
      const remindersList = sampleReminders
        .map((reminder) => {
          const priorityIcon =
            reminder.priority === 'urgent' ? '🔴' : reminder.priority === 'high' ? '🟠' : '🟡'
          const statusText =
            reminder.dueIn === 'overdue' ? '⚠️ OVERDUE' : `⏰ Due in ${reminder.dueIn}`

          return `### ${priorityIcon} ${reminder.subject}
**From:** ${reminder.from}  
**Priority:** ${reminder.priority.toUpperCase()}  
**Status:** ${statusText}  
**Action:** ${reminder.description}

---`
        })
        .join('\n')

      return {
        content: `## 📋 Your Email Reminders

Here are your current email reminders:

${remindersList}

### 📊 Reminder Summary
- **Total Active**: ${sampleReminders.length}
- **Overdue**: ${sampleReminders.filter((r) => r.dueIn === 'overdue').length}
- **Due Today**: ${sampleReminders.filter((r) => r.dueIn.includes('hour')).length}
- **Upcoming**: ${sampleReminders.filter((r) => r.dueIn.includes('day')).length}

**Pro Tip**: You can snooze, complete, or reschedule any reminder by clicking the action buttons when they appear in our chat!`,
        followUpQuestions: [
          'Complete the overdue expense report reminder',
          'Snooze budget proposal reminder for 2 hours',
          'View the client presentation email',
          'Create a new reminder',
        ],
        messageType: 'structured',
      }
    }

    if (
      lowerMessage.includes('overdue') ||
      lowerMessage.includes('urgent') ||
      lowerMessage.includes('important')
    ) {
      return {
        content: `## 🚨 Urgent Email Reminders Alert!

You have **1 overdue** and **2 urgent** email reminders that need immediate attention:

### 🔴 OVERDUE (1)
**Expense Report Submission**
- From: <EMAIL>
- **Status**: 1 day overdue
- **Action**: Submit monthly expense reports immediately

### 🟠 HIGH PRIORITY (2)
**Budget Proposal Follow-up**
- From: <EMAIL>  
- Due in: 2 hours
- Action needed: Respond to Q4 budget allocation

**Client Presentation Review**
- From: <EMAIL>
- Due in: 1 day  
- Action needed: Final review before client meeting

### ⚡ Quick Actions Available
I can help you:
- **View emails** directly in your email client
- **Compose replies** with AI assistance
- **Reschedule** reminders to better times
- **Mark complete** when actions are done
- **Set follow-up** reminders if needed

**Which reminder would you like to handle first?**`,
        followUpQuestions: [
          'Open expense report email immediately',
          'Help me respond to budget proposal',
          'View client presentation email',
          'Snooze all reminders for 1 hour',
        ],
        messageType: 'structured',
      }
    }

    // Default reminder response
    return {
      content: `## 🤖 AMNA Email Reminder Assistant

I'm here to help you never miss important emails again! Here's what I can do:

### 🎯 Current Reminder Status
- **Active Reminders**: 3
- **Due Today**: 2  
- **Overdue**: 1
- **This Week**: 5

### 🔔 Smart Reminders I Can Create
- **Follow-up reminders** for emails awaiting responses
- **Deadline alerts** for time-sensitive emails
- **Meeting preparation** reminders
- **Payment due date** notifications
- **Custom reminders** for any email

### 💡 How It Works
1. **Tell me about an email**: "Remind me about the proposal from Sarah"
2. **I'll analyze it**: Check importance, deadlines, sender priority
3. **Smart scheduling**: Suggest optimal reminder times
4. **Chat notifications**: Get reminders right here
5. **Quick actions**: Complete, snooze, or reschedule easily

### 📱 Natural Commands
Try saying:
- "Remind me to follow up on the client email tomorrow"
- "Set a deadline reminder for the project proposal" 
- "Don't let me forget about the meeting prep email"
- "Show me all my email reminders"

**What email would you like me to help you remember?**`,
      followUpQuestions: [
        'Show me my current reminders',
        'Create a reminder for important email',
        'Scan emails for deadline reminders',
        'Configure reminder preferences',
      ],
      messageType: 'structured',
    }
  }

  private generateAuthenticationResponse(message: string): ChatResponse {
    const authScenarios = [
      {
        type: 'security-overview',
        title: 'Security & Authentication',
        content: `## 🔐 Account Security Overview

Your account security status and available features:

### Security Score: 85/100
**Good** - Your account is well-protected

### Active Security Features
- ✅ **Strong Password**: Last changed 45 days ago
- ✅ **Email Verified**: Primary email confirmed
- ❌ **Two-Factor Authentication**: Not enabled (recommended)
- ✅ **Trusted Devices**: 2 devices registered

### Recent Activity
| Action | Device | Location | Time |
|--------|--------|----------|------|
| Successful login | Chrome/MacOS | New York, US | 2 hours ago |
| Password viewed | Mobile App | New York, US | 1 day ago |
| Settings updated | Firefox/Windows | Boston, US | 3 days ago |

### Security Recommendations
1. **Enable 2FA** - Add an extra layer of security
2. **Review Devices** - Remove any unrecognized devices
3. **Update Recovery** - Add backup email/phone`,
        followUpQuestions: [
          'Enable two-factor authentication',
          'View all login history',
          'Add trusted devices',
          'Update security settings',
        ],
      },
      {
        type: '2fa-setup',
        title: 'Two-Factor Authentication',
        content: `## 🔐 Two-Factor Authentication Setup

Let's secure your account with 2FA:

### Step 1: Choose Your Method
- **📱 Authenticator App** (Recommended)
  - Google Authenticator, Authy, or 1Password
  - Most secure option
  - Works offline

- **📧 Email Codes**
  - Codes sent to your verified email
  - Good backup option
  - No app required

- **🔑 Hardware Key** (Coming Soon)
  - YubiKey support
  - Maximum security

### Step 2: Backup Codes
You'll receive 5 backup codes:
- Store them securely
- Each code works only once
- Use if you lose access to your device

### Benefits of 2FA
- 🛡️ 99.9% reduction in account takeovers
- 🔒 Protection against password theft
- 🎯 Targeted phishing protection

Ready to set up 2FA?`,
        followUpQuestions: [
          'Set up authenticator app',
          'Use email for 2FA',
          'What if I lose my phone?',
          'Skip for now',
        ],
      },
    ]

    const scenario = authScenarios[Math.floor(Math.random() * authScenarios.length)]

    return {
      content: scenario.content,
      followUpQuestions: scenario.followUpQuestions,
      messageType: 'structured',
    }
  }

  private generateAnalyticsResponse(message: string): ChatResponse {
    const sources: Source[] = [
      {
        id: '1',
        title: 'Real-time Analytics Dashboard',
        url: '/analytics/dashboard',
        snippet:
          'View comprehensive metrics and insights about your chat usage, performance, and user engagement.',
        favicon: '/favicon.ico',
      },
    ]

    const analyticsData = {
      chatMetrics: {
        totalConversations: 156,
        avgMessagesPerChat: 12.4,
        avgResponseTime: '1.2s',
        satisfactionScore: 4.7,
      },
      topTopics: [
        { topic: 'React Development', percentage: 35 },
        { topic: 'TypeScript', percentage: 28 },
        { topic: 'API Integration', percentage: 20 },
        { topic: 'Performance', percentage: 17 },
      ],
      timeSeriesData: [
        { day: 'Mon', messages: 145 },
        { day: 'Tue', messages: 189 },
        { day: 'Wed', messages: 201 },
        { day: 'Thu', messages: 167 },
        { day: 'Fri', messages: 234 },
      ],
    }

    return {
      content: `## 📊 Analytics & Performance Insights

Based on your usage patterns, here's a comprehensive analysis:

### Chat Metrics (Last 7 Days)
- **Total Conversations**: ${analyticsData.chatMetrics.totalConversations}
- **Avg Messages/Chat**: ${analyticsData.chatMetrics.avgMessagesPerChat}
- **Avg Response Time**: ${analyticsData.chatMetrics.avgResponseTime}
- **Satisfaction Score**: ${analyticsData.chatMetrics.satisfactionScore}/5.0 *

### Top Discussion Topics
${analyticsData.topTopics.map((t) => `- **${t.topic}**: ${t.percentage}%`).join('\n')}

### Activity Trend
\`\`\`
Messages per Day
250 |                    ╭─╮
200 |           ╭─╮  ╭──╯ │
150 | ──╮  ╭────╯ ╰──╯     ╰─
100 |   ╰──╯
    └─────────────────────────
     Mon  Tue  Wed  Thu  Fri
\`\`\`

### AI Performance Metrics
| Model | Avg Response | Success Rate | Cost/Query |
|-------|-------------|--------------|------------|
| GPT-4 | 1.8s | 97% | $0.012 |
| Claude | 1.2s | 98% | $0.008 |
| Gemini | 1.5s | 96% | $0.010 |

### Insights & Recommendations
- 📈 **Peak Usage**: Thursdays 2-4 PM (optimize for this time)
- 🎯 **Most Valuable**: Code debugging sessions (45 min avg)
- 💡 **Suggestion**: Enable caching for frequent queries to reduce response time by 40%`,
      sources,
      followUpQuestions: [
        'Show me detailed user engagement metrics',
        'Analyze my coding session productivity',
        'Compare this week vs last week',
        'Export analytics report as PDF',
      ],
      messageType: 'table',
    }
  }

  private generateComponentDemoResponse(message: string): ChatResponse {
    const componentDemos = [
      {
        type: 'card-showcase',
        content: `## 🎴 Interactive Card Components

I've prepared some card components to demonstrate our UI capabilities:

### Available Card Types
1. **Info Card** - Display important information
2. **Stats Card** - Show metrics and KPIs  
3. **Action Card** - Interactive elements with CTAs
4. **Media Card** - Rich media content display

### Demo: Stats Card
\`\`\`jsx
<StatsCard
  title="Performance Metrics"
  stats={[
    { label: "Response Time", value: "1.2s", trend: "up" },
    { label: "Success Rate", value: "98%", trend: "stable" },
    { label: "User Satisfaction", value: "4.8/5", trend: "up" }
  ]}
  actions={[
    { label: "View Details", onClick: handleDetails },
    { label: "Export", onClick: handleExport }
  ]}
/>
\`\`\`

### Live Preview
┌─────────────────────────────┐
│ 📊 Performance Metrics      │
├─────────────────────────────┤
│ Response Time    ↑ 1.2s    │
│ Success Rate     → 98%     │
│ User Satisfaction ↑ 4.8/5   │
├─────────────────────────────┤
│ [View Details] [Export]     │
└─────────────────────────────┘`,
        followUpQuestions: [
          'Show me modal components',
          'Demonstrate widget examples',
          'Create a custom dashboard',
          'View component documentation',
        ],
      },
      {
        type: 'modal-demo',
        content: `## 🪟 Modal & Dialog Components

Our modal system supports various interaction patterns:

### Modal Types
- **Dialog**: Simple confirmations and alerts
- **Fullscreen**: Immersive content experiences
- **Drawer**: Slide-in panels from edges
- **Bottom Sheet**: Mobile-friendly overlays

### Example: Confirmation Dialog
\`\`\`typescript
const showConfirmation = () => {
  openModal({
    type: 'dialog',
    title: 'Confirm Action',
    content: 'Are you sure you want to proceed?',
    actions: [
      { label: 'Cancel', variant: 'secondary' },
      { label: 'Confirm', variant: 'primary', onClick: handleConfirm }
    ]
  })
}
\`\`\`

### Interactive Features
- **Backdrop Click**: Configurable dismiss behavior
- **Keyboard Navigation**: ESC to close, Tab through actions
- **Animation**: Smooth transitions with Framer Motion
- **Stacking**: Multiple modals with proper z-index management`,
        followUpQuestions: [
          'Show me a fullscreen modal example',
          'How to create custom modal content?',
          'Implement a multi-step wizard modal',
          'Best practices for modal accessibility',
        ],
      },
    ]

    const demo = componentDemos[Math.floor(Math.random() * componentDemos.length)]

    return {
      content: demo.content,
      followUpQuestions: demo.followUpQuestions,
      messageType: 'structured',
    }
  }

  private generateRealtimeResponse(message: string): ChatResponse {
    return {
      content: `## 🔴 Real-time Features Demo

Experience our live, real-time capabilities:

### Active Real-time Connections
- **WebSocket Status**: 🟢 Connected
- **Latency**: 23ms
- **Active Users**: 847 online now
- **Message Queue**: 0 pending

### Real-time Features Available

#### 💬 Live Chat Updates
- Instant message delivery
- Typing indicators
- Read receipts
- Presence status

#### 📊 Live Analytics
\`\`\`
Current Activity (Live)
Users Online: ●●●●●●●●●● 847
Messages/min: ████████░░ 82
API Calls/s:  ██████░░░░ 64
\`\`\`

#### 🔄 Sync Features
- **Multi-device Sync**: Changes reflect instantly
- **Collaborative Editing**: See others' edits live
- **Real-time Notifications**: Push updates as they happen
- **Live Data Streaming**: Continuous data flow

### Demo: Live Collaboration
*Simulating real-time collaboration...*

👤 **Alex** is typing...
👤 **Maya** just joined the conversation
📝 **Sam** is editing the document
🔔 **New notification**: "Build completed successfully"

### Performance Metrics
- **Message Delivery**: < 100ms globally
- **Sync Latency**: < 50ms average
- **Uptime**: 99.99% this month`,
      followUpQuestions: [
        'Start a live collaboration session',
        'View real-time performance metrics',
        'Test WebSocket connection speed',
        'Enable desktop notifications',
      ],
      messageType: 'structured',
    }
  }

  getChatHistory(chatId: string): Message[] {
    return this.chatHistory[chatId] || []
  }

  saveChatHistory(chatId: string, messages: Message[]): void {
    if (!this.chatHistory[chatId]) {
      this.chatHistory[chatId] = []
    }
    this.chatHistory[chatId].push(...messages)
  }

  getAllChats(): Array<{
    id: string
    title: string
    lastMessage: Date
    type?: string
    messageCount: number
  }> {
    return Object.entries(this.chatHistory).map(([id, messages]) => ({
      id,
      title: this.generateChatTitle(messages[0]?.content) || 'New Chat',
      lastMessage: messages[messages.length - 1]?.timestamp || new Date(),
      type: this.inferChatType(messages),
      messageCount: messages.length,
    }))
  }

  private generateChatTitle(firstMessage?: string): string {
    if (!firstMessage) {
      return 'New Chat'
    }

    const message = firstMessage.toLowerCase()

    // Generate contextual titles based on content
    if (message.includes('debug') || message.includes('error') || message.includes('fix')) {
      return '🔧 Debug Session'
    }
    if (message.includes('learn') || message.includes('explain') || message.includes('how')) {
      return '📚 Learning Session'
    }
    if (message.includes('create') || message.includes('write') || message.includes('generate')) {
      return '✨ Creative Workshop'
    }
    if (message.includes('analyze') || message.includes('data') || message.includes('research')) {
      return '📊 Research & Analysis'
    }
    if (
      message.includes('plan') ||
      message.includes('strategy') ||
      message.includes('architecture')
    ) {
      return '🏗️ Planning Session'
    }
    if (message.includes('career') || message.includes('interview') || message.includes('job')) {
      return '🚀 Career Discussion'
    }

    // Fallback to first few words
    return firstMessage.slice(0, 30) + (firstMessage.length > 30 ? '...' : '')
  }

  private inferChatType(messages: any[]): string {
    const firstMessage = messages[0]?.content?.toLowerCase() || ''

    if (firstMessage.includes('debug') || firstMessage.includes('error')) {
      return 'troubleshooting'
    }
    if (firstMessage.includes('create') || firstMessage.includes('write')) {
      return 'creative'
    }
    if (firstMessage.includes('analyze') || firstMessage.includes('data')) {
      return 'research'
    }
    if (firstMessage.includes('learn') || firstMessage.includes('explain')) {
      return 'educational'
    }
    if (firstMessage.includes('plan') || firstMessage.includes('architecture')) {
      return 'planning'
    }

    return 'general'
  }
}

// Pre-populate with some sample conversations for demo
const mockAPI = new MockChatAPI()

// Add sample conversation history
const sampleConversations = [
  {
    id: 'conv_1',
    messages: [
      {
        id: 'msg_1',
        role: 'user' as const,
        content: 'How can I optimize React component performance?',
        timestamp: new Date(Date.now() - 86400000), // 1 day ago
      },
      {
        id: 'msg_2',
        role: 'assistant' as const,
        content: 'Here are key strategies for React performance optimization...',
        timestamp: new Date(Date.now() - 86400000 + 30000),
      },
    ],
  },
  {
    id: 'conv_2',
    messages: [
      {
        id: 'msg_3',
        role: 'user' as const,
        content: 'Debug this TypeScript error: Cannot read property of undefined',
        timestamp: new Date(Date.now() - 3600000), // 1 hour ago
      },
    ],
  },
  {
    id: 'conv_3',
    messages: [
      {
        id: 'msg_4',
        role: 'user' as const,
        content: 'Create a story about AI helping developers',
        timestamp: new Date(Date.now() - 1800000), // 30 minutes ago
      },
    ],
  },
]

// Populate sample data
sampleConversations.forEach((conv) => {
  mockAPI.saveChatHistory(conv.id, conv.messages)
})

export const mockChatAPI = mockAPI
