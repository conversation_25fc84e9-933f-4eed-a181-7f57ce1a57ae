import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { getAuthToken, clearAuthToken } from '../auth/auth.service'

// API Version configuration
const API_VERSION = 'v1'
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api'

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for auth and version headers
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token
    const token = getAuthToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // Add API version header
    config.headers['X-API-Version'] = API_VERSION

    // Add request ID for tracing
    config.headers['X-Request-ID'] = generateRequestId()

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling and version warnings
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // Check for deprecation warnings
    const deprecated = response.headers['x-api-deprecated']
    const sunsetDate = response.headers['x-api-sunset-date']

    if (deprecated === 'true') {
      console.warn(
        `[API Deprecation Warning] This endpoint is deprecated and will be removed on ${sunsetDate}`
      )

      // You might want to send this to your monitoring service
      if (window.Sentry) {
        window.Sentry.captureMessage(
          `API Deprecation: ${response.config.url}`,
          'warning'
        )
      }
    }

    return response
  },
  async (error) => {
    // Handle 401 Unauthorized
    if (error.response?.status === 401) {
      clearAuthToken()
      window.location.href = '/login'
    }

    // Handle 503 Service Unavailable (Circuit breaker open)
    if (error.response?.status === 503) {
      console.error('Service temporarily unavailable. Circuit breaker may be open.')
    }

    // Handle version mismatch
    if (error.response?.status === 400 && error.response?.data?.error?.includes('version')) {
      console.error('API version mismatch. Please refresh the application.')
    }

    return Promise.reject(error)
  }
)

// Generate unique request ID for tracing
function generateRequestId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// API client wrapper with retry logic
export class ApiClient {
  private static instance: ApiClient

  static getInstance(): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient()
    }
    return ApiClient.instance
  }

  // GET request with automatic retry
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await apiClient.get<T>(url, config)
    return response.data
  }

  // POST request
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await apiClient.post<T>(url, data, config)
    return response.data
  }

  // PUT request
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await apiClient.put<T>(url, data, config)
    return response.data
  }

  // PATCH request
  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await apiClient.patch<T>(url, data, config)
    return response.data
  }

  // DELETE request
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await apiClient.delete<T>(url, config)
    return response.data
  }

  // Get available API versions
  async getVersions(): Promise<{ current: string; available: string[]; deprecated: string[] }> {
    const response = await axios.get(`${API_BASE_URL}/api/versions`)
    return response.data
  }

  // Health check
  async healthCheck(): Promise<any> {
    const response = await apiClient.get('/health')
    return response
  }
}

// Export singleton instance
export const api = ApiClient.getInstance()

// Export types
export type { AxiosRequestConfig, AxiosResponse }

// Specific API endpoints with versioning
export const endpoints = {
  // Auth endpoints
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    refresh: '/auth/refresh',
    logout: '/auth/logout',
    profile: '/auth/me',
  },

  // User endpoints
  users: {
    list: '/users',
    get: (id: string) => `/users/${id}`,
    create: '/users',
    update: (id: string) => `/users/${id}`,
    delete: (id: string) => `/users/${id}`,
  },

  // Training endpoints
  training: {
    modules: '/training/modules',
    module: (id: string) => `/training/modules/${id}`,
    progress: '/training/progress',
    complete: (moduleId: string) => `/training/modules/${moduleId}/complete`,
  },

  // Analytics endpoints
  analytics: {
    dashboard: '/analytics/dashboard',
    reports: '/analytics/reports',
    export: '/analytics/export',
  },

  // L&D specific endpoints
  learning: {
    paths: '/learning/paths',
    path: (id: string) => `/learning/paths/${id}`,
    recommendations: '/learning/recommendations',
    skills: '/learning/skills',
  },

  // Integration endpoints
  integrations: {
    list: '/integrations',
    connect: (provider: string) => `/integrations/${provider}/connect`,
    disconnect: (provider: string) => `/integrations/${provider}/disconnect`,
    sync: (provider: string) => `/integrations/${provider}/sync`,
  },

  // Notification endpoints
  notifications: {
    list: '/notifications',
    markRead: (id: string) => `/notifications/${id}/read`,
    markAllRead: '/notifications/read-all',
    preferences: '/notifications/preferences',
  },

  // File upload endpoints
  files: {
    upload: '/files/upload',
    download: (id: string) => `/files/${id}/download`,
    delete: (id: string) => `/files/${id}`,
  },

  // Search endpoints
  search: {
    global: '/search',
    users: '/search/users',
    content: '/search/content',
    skills: '/search/skills',
  },

  // Admin endpoints
  admin: {
    dashboard: '/admin/dashboard',
    users: '/admin/users',
    settings: '/admin/settings',
    audit: '/admin/audit-logs',
  },
}

// Fallback handlers for critical endpoints
export const fallbackHandlers = {
  // Return cached user data if available
  getUserFallback: async (userId: string) => {
    const cached = localStorage.getItem(`user_${userId}`)
    if (cached) {
      return JSON.parse(cached)
    }
    return null
  },

  // Return empty list for non-critical list endpoints
  getEmptyListFallback: () => ({
    data: [],
    total: 0,
    page: 1,
    limit: 10,
    hasMore: false,
  }),

  // Return cached dashboard data
  getDashboardFallback: async () => {
    const cached = localStorage.getItem('dashboard_cache')
    if (cached) {
      const data = JSON.parse(cached)
      // Check if cache is less than 1 hour old
      if (Date.now() - data.timestamp < 3600000) {
        return data.payload
      }
    }
    return null
  },
}

// Version migration helpers
export const versionMigration = {
  // Check if client needs to be updated
  checkVersion: async () => {
    try {
      const versions = await api.getVersions()
      if (versions.deprecated.includes(API_VERSION)) {
        console.warn(`Client is using deprecated API version ${API_VERSION}`)
        return { needsUpdate: true, currentVersion: versions.current }
      }
      return { needsUpdate: false, currentVersion: versions.current }
    } catch (error) {
      console.error('Failed to check API version', error)
      return { needsUpdate: false, currentVersion: API_VERSION }
    }
  },

  // Show version update notification
  showUpdateNotification: (newVersion: string) => {
    // This would typically trigger a UI notification
    console.warn(`Please update to API version ${newVersion}`)
  },
}

// Initialize version check on app start
if (typeof window !== 'undefined') {
  versionMigration.checkVersion().then(({ needsUpdate, currentVersion }) => {
    if (needsUpdate) {
      versionMigration.showUpdateNotification(currentVersion)
    }
  })
}

export default apiClient
